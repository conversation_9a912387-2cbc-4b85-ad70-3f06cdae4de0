#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Test ultra-rapide de l'optimiseur corrigé
"""

import os
import sys
import warnings

# Suppression des warnings
warnings.filterwarnings('ignore')
os.environ['TF_CPP_MIN_LOG_LEVEL'] = '3'

def test_ultra_rapide():
    """Test ultra-rapide avec les corrections"""
    print("🚀 TEST ULTRA-RAPIDE DE L'OPTIMISEUR CORRIGÉ")
    print("=" * 60)
    
    try:
        print("📦 Import des modules...")
        from keno_data import KenoDataManager
        from keno_analyzer import KenoAnalyzer
        from keno_ultra_optimizer import KenoUltraOptimizer
        print("✅ Modules importés")
        
        print("\n🔧 Initialisation...")
        data_manager = KenoDataManager()
        analyzer = KenoAnalyzer(data_manager)
        ultra_optimizer = KenoUltraOptimizer(data_manager, analyzer)
        print("✅ Composants initialisés")
        
        print("\n📊 Chargement des données...")
        data_path = os.path.join("data", "datafull.keno")
        if os.path.exists(data_path):
            success = data_manager.load_database(data_path)
            if success:
                draws_count = data_manager.get_draws_count()
                print(f"✅ {draws_count} tirages chargés")
            else:
                print("❌ Échec du chargement")
                return False
        else:
            print(f"❌ Fichier non trouvé: {data_path}")
            return False
        
        print("\n🧠 Test de création de caractéristiques (version rapide)...")
        test_number = 7
        
        # Test avec paramètres ultra-réduits
        result = ultra_optimizer.create_ultra_features(test_number, lookback_days=180, max_samples=200)
        
        if result:
            X, y, feature_names = result
            print(f"✅ {len(feature_names)} caractéristiques créées pour {len(X)} échantillons")
            
            print("\n🤖 Test d'entraînement ultra-rapide...")
            model_result = ultra_optimizer.train_ultra_model(test_number, mode="fast")
            
            if model_result:
                perf = model_result['performance']
                print(f"✅ Modèle entraîné avec succès")
                print(f"🎯 F1-score: {perf['f1_score']:.4f}")
                print(f"📊 Précision: {perf['accuracy']:.4f}")
                print(f"🔢 Échantillons de test: {perf['test_size']}")
                
                print("\n🔮 Test de prédiction...")
                prob = ultra_optimizer.predict_number_probability(test_number)
                print(f"✅ Probabilité calculée: {prob:.4f}")
                
                print("\n🎉 TEST ULTRA-RAPIDE RÉUSSI ! 🎉")
                print("✨ L'optimiseur ultra fonctionne maintenant correctement")
                print("🚀 Vous pouvez utiliser l'auto-amélioration ultra dans l'interface")
                return True
            else:
                print("❌ Échec de l'entraînement")
                return False
        else:
            print("❌ Échec de création des caractéristiques")
            return False
            
    except Exception as e:
        print(f"\n❌ ERREUR: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = test_ultra_rapide()
    
    print("\n" + "=" * 60)
    if success:
        print("🎉 SUCCÈS ! L'AUTO-AMÉLIORATION ULTRA EST MAINTENANT OPÉRATIONNELLE !")
        print("\n📋 Instructions pour utiliser l'auto-amélioration ultra :")
        print("1. 🚀 Lancez l'application : python main.py")
        print("2. 🎯 Cliquez sur 'LANCER L'AUTO-AMÉLIORATION'")
        print("3. ⚡ Sélectionnez le mode 'ULTRA-RAPIDE'")
        print("4. 🚀 Cliquez sur 'DÉMARRER L'ULTRA-OPTIMISATION'")
        print("\n✨ Le processus devrait maintenant fonctionner sans blocage !")
    else:
        print("❌ ÉCHEC - Il reste des problèmes à résoudre")
    
    print("=" * 60)
