#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Script pour diagnostiquer et corriger l'auto-amélioration ultra
"""

import os
import sys

def check_ultra_system():
    """Vérifie l'état du système ultra"""
    print("🔍 DIAGNOSTIC DU SYSTÈME ULTRA-OPTIMISÉ")
    print("=" * 50)
    
    # 1. Vérifier les fichiers
    print("📁 Vérification des fichiers...")
    
    files_to_check = [
        "keno_ultra_optimizer.py",
        "keno_gui.py",
        "main.py"
    ]
    
    for file in files_to_check:
        if os.path.exists(file):
            print(f"  ✅ {file} existe")
        else:
            print(f"  ❌ {file} manquant")
    
    # 2. Vérifier l'import
    print("\n📦 Test d'import...")
    try:
        import keno_ultra_optimizer
        print("  ✅ keno_ultra_optimizer importé")
        
        # Vérifier la classe
        if hasattr(keno_ultra_optimizer, 'KenoUltraOptimizer'):
            print("  ✅ Classe KenoUltraOptimizer trouvée")
            
            # Vérifier les méthodes importantes
            cls = keno_ultra_optimizer.KenoUltraOptimizer
            methods_to_check = [
                'create_ultra_features',
                'train_ultra_model',
                'predict_number_probability',
                'get_ultra_predictions'
            ]
            
            for method in methods_to_check:
                if hasattr(cls, method):
                    print(f"    ✅ Méthode {method} présente")
                else:
                    print(f"    ❌ Méthode {method} manquante")
        else:
            print("  ❌ Classe KenoUltraOptimizer non trouvée")
            
    except Exception as e:
        print(f"  ❌ Erreur d'import: {e}")
    
    # 3. Vérifier l'interface
    print("\n🖥️ Vérification de l'interface...")
    try:
        with open("keno_gui.py", "r", encoding="utf-8") as f:
            content = f.read()
            
        if "show_auto_improve_dialog" in content:
            print("  ✅ Méthode show_auto_improve_dialog trouvée")
        else:
            print("  ❌ Méthode show_auto_improve_dialog manquante")
            
        if "_start_ultra_auto_improve" in content:
            print("  ✅ Méthode _start_ultra_auto_improve trouvée")
        else:
            print("  ❌ Méthode _start_ultra_auto_improve manquante")
            
        if "🚀 Auto-Amélioration ULTRA 🚀" in content:
            print("  ✅ Interface ultra trouvée")
        else:
            print("  ❌ Interface ultra manquante")
            
    except Exception as e:
        print(f"  ❌ Erreur lecture interface: {e}")
    
    print("\n" + "=" * 50)

def create_minimal_test():
    """Crée un test minimal pour l'auto-amélioration"""
    print("🧪 CRÉATION D'UN TEST MINIMAL")
    print("=" * 50)
    
    test_content = '''#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""Test minimal de l'auto-amélioration ultra"""

def test_minimal():
    print("🚀 Test minimal de l'auto-amélioration ultra")
    
    try:
        # Test d'import basique
        print("📦 Import des modules...")
        from keno_ultra_optimizer import KenoUltraOptimizer
        print("✅ Import réussi")
        
        # Test de création d'instance
        print("🔧 Création d'instance...")
        
        # Mock très simple
        class MockData:
            def __init__(self):
                self.draws = []
        
        class MockAnalyzer:
            def __init__(self, data):
                self.data = data
        
        data = MockData()
        analyzer = MockAnalyzer(data)
        optimizer = KenoUltraOptimizer(data, analyzer)
        
        print("✅ Instance créée avec succès")
        print("🎉 Test minimal réussi !")
        return True
        
    except Exception as e:
        print(f"❌ Erreur: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = test_minimal()
    if success:
        print("✨ Le système ultra fonctionne au niveau basique")
    else:
        print("🔧 Il y a un problème fondamental à résoudre")
'''
    
    with open("minimal_ultra_test.py", "w", encoding="utf-8") as f:
        f.write(test_content)
    
    print("✅ Test minimal créé: minimal_ultra_test.py")

def suggest_fixes():
    """Suggère des corrections"""
    print("\n💡 SUGGESTIONS DE CORRECTION")
    print("=" * 50)
    
    print("1. 🔄 Redémarrer l'application principale:")
    print("   python main.py")
    
    print("\n2. 🧪 Tester le système ultra:")
    print("   python minimal_ultra_test.py")
    
    print("\n3. 🎯 Dans l'interface graphique:")
    print("   - Cliquer sur 'LANCER L'AUTO-AMÉLIORATION'")
    print("   - Sélectionner le mode 'ULTRA-RAPIDE'")
    print("   - Cliquer sur 'DÉMARRER L'ULTRA-OPTIMISATION'")
    
    print("\n4. 🔍 Si ça ne marche pas:")
    print("   - Vérifier les messages d'erreur dans la console")
    print("   - Vérifier que les données sont chargées (>100 tirages)")
    print("   - Essayer le mode 'STANDARD' en fallback")

if __name__ == "__main__":
    check_ultra_system()
    create_minimal_test()
    suggest_fixes()
    
    print("\n🎯 RÉSUMÉ:")
    print("Le système ultra-optimisé a été amélioré avec:")
    print("✨ Méthodes d'entraînement complètes")
    print("🎯 Prédictions ultra-avancées") 
    print("💾 Sauvegarde/chargement des modèles")
    print("⚡ Optimisations de performance")
    print("\nEssayez maintenant l'auto-amélioration dans l'interface !")
