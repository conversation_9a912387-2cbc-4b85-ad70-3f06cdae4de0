#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Système d'optimisation ultra-avancé pour les prédictions Keno
Version améliorée avec suppression des warnings et performances optimisées
"""

import os
import sys
import json
import numpy as np
import pandas as pd
from datetime import datetime, timedelta
import warnings

# Suppression complète de tous les warnings
warnings.filterwarnings('ignore')
os.environ['PYTHONWARNINGS'] = 'ignore'
os.environ['TF_CPP_MIN_LOG_LEVEL'] = '3'

# Imports conditionnels optimisés
try:
    from sklearn.ensemble import RandomForestClassifier, VotingClassifier, GradientBoostingClassifier
    from sklearn.model_selection import train_test_split, RandomizedSearchCV, StratifiedKFold
    from sklearn.metrics import accuracy_score, precision_score, recall_score, f1_score
    from sklearn.feature_selection import SelectKBest, f_classif, mutual_info_classif
    from sklearn.preprocessing import RobustScaler
    sklearn_available = True
except ImportError:
    sklearn_available = False

try:
    import xgboost as xgb
    # Supprimer les warnings XGBoost
    xgb.set_config(verbosity=0)
    xgboost_available = True
except ImportError:
    xgboost_available = False

try:
    import lightgbm as lgb
    lightgbm_available = True
except ImportError:
    lightgbm_available = False

class KenoUltraOptimizer:
    """Optimiseur ultra-avancé pour les prédictions Keno"""

    def __init__(self, data_manager, analyzer):
        self.data_manager = data_manager
        self.analyzer = analyzer
        self.models = {}
        self.feature_importance = {}
        self.performance_history = {}

        # Configuration ultra-optimisée
        self.config = {
            'feature_engineering': {
                'use_advanced_patterns': True,
                'use_fourier_analysis': True,
                'use_statistical_moments': True,
                'use_entropy_features': True,
                'lookback_windows': [3, 5, 10, 20, 50, 100, 200],
                'rolling_windows': [3, 7, 14, 21, 30],
                'lag_features': [1, 2, 3, 5, 10]
            },
            'model_optimization': {
                'use_ensemble_stacking': True,
                'use_bayesian_optimization': True,
                'use_early_stopping': True,
                'cross_validation_folds': 10,
                'optimization_iterations': 50,
                'ensemble_weights_optimization': True
            },
            'prediction_strategy': {
                'use_multi_timeframe_analysis': True,
                'use_confidence_intervals': True,
                'use_trend_prediction': True,
                'use_volatility_adjustment': True,
                'use_adaptive_learning': True
            }
        }

    def create_ultra_features(self, number, lookback_days=365, max_samples=500):
        """Crée des caractéristiques ultra-avancées (version optimisée)"""
        print(f"Création de caractéristiques ultra-avancées pour le numéro {number}...")

        draws = self.data_manager.draws
        if len(draws) < 100:
            print("Pas assez de données pour l'analyse ultra-avancée")
            return None

        # Filtrer et limiter drastiquement pour éviter les blocages
        if lookback_days:
            cutoff_date = datetime.now() - timedelta(days=lookback_days)
            draws = [d for d in draws if d.draw_date and d.draw_date >= cutoff_date]

        # Limitation stricte pour mode ultra-rapide
        if len(draws) > max_samples:
            draws = draws[-max_samples:]
            print(f"Limitation à {max_samples} tirages récents pour mode ultra-rapide")

        features_data = []
        targets = []

        # Fenêtre de départ très réduite
        start_index = min(50, len(draws) // 3)
        total_iterations = len(draws) - start_index

        print(f"Traitement de {total_iterations} échantillons...")

        for i in range(start_index, len(draws)):
            # Affichage de progression pour éviter l'impression de blocage
            if i % 50 == 0:
                progress = ((i - start_index) / total_iterations) * 100
                print(f"  Progression: {progress:.1f}% ({i-start_index}/{total_iterations})")

            current_draw = draws[i]
            draw_numbers = self._get_draw_numbers(current_draw)

            if not draw_numbers:
                continue

            features = {}

            # 1. Features de fréquence simplifiées
            for window in [5, 10, 20]:  # Encore moins de fenêtres
                recent_draws = draws[max(0, i-window):i]
                freq = sum(1 for d in recent_draws if self._get_draw_numbers(d) and number in self._get_draw_numbers(d))

                features[f'freq_{window}'] = freq / max(1, window)
                features[f'freq_norm_{window}'] = freq / max(1, len(recent_draws))

            # 2. Features de momentum simplifiées
            try:
                momentum_features = self._calculate_simple_momentum(draws[:i], number)
                features.update(momentum_features)
            except Exception as e:
                print(f"Erreur momentum: {e}")
                features.update({'momentum_5': 0, 'momentum_10': 0})

            # 3. Features statistiques de base
            try:
                statistical_features = self._calculate_simple_stats(draws[:i], number)
                features.update(statistical_features)
            except Exception as e:
                print(f"Erreur stats: {e}")
                features.update({'gap_current': 0, 'gap_mean': 0})

            # 4. Features temporelles basiques
            try:
                temporal_features = self._calculate_simple_temporal(current_draw)
                features.update(temporal_features)
            except Exception as e:
                print(f"Erreur temporal: {e}")
                features.update({'day_of_week': 0, 'hour': 12})

            if len(features) > 0:
                features_data.append(list(features.values()))
                targets.append(1 if number in draw_numbers else 0)

        if not features_data:
            print(f"Aucune donnée créée pour le numéro {number}")
            return None

        # Créer les noms de features
        feature_names = list(features.keys())
        df = pd.DataFrame(features_data, columns=feature_names)

        print(f"✅ Créé {len(feature_names)} caractéristiques pour {len(df)} échantillons")
        return df, targets, feature_names

    def _get_draw_numbers(self, draw):
        """Fonction utilitaire pour obtenir les numéros d'un tirage"""
        if hasattr(draw, 'draw_numbers') and draw.draw_numbers:
            return draw.draw_numbers
        elif hasattr(draw, 'numbers') and draw.numbers:
            return draw.numbers
        return None

    def _calculate_advanced_momentum(self, draws, number):
        """Calcule des features de momentum avancées"""
        features = {}

        # Momentum multi-échelles
        for window in [3, 5, 10, 20, 50]:
            recent = draws[-window:] if len(draws) >= window else draws
            momentum = sum(1 for d in recent if self._get_draw_numbers(d) and number in self._get_draw_numbers(d))
            features[f'momentum_{window}'] = momentum / len(recent)

        # Accélération du momentum
        if len(draws) >= 20:
            recent_10 = draws[-10:]
            previous_10 = draws[-20:-10]

            recent_freq = sum(1 for d in recent_10 if self._get_draw_numbers(d) and number in self._get_draw_numbers(d)) / 10
            previous_freq = sum(1 for d in previous_10 if self._get_draw_numbers(d) and number in self._get_draw_numbers(d)) / 10

            features['momentum_acceleration'] = recent_freq - previous_freq
            features['momentum_trend'] = recent_freq / max(0.001, previous_freq)
        else:
            features['momentum_acceleration'] = 0
            features['momentum_trend'] = 1

        return features

    def _calculate_simple_momentum(self, draws, number):
        """Version simplifiée du calcul de momentum"""
        features = {}

        # Momentum sur 5 et 10 derniers tirages seulement
        for window in [5, 10]:
            recent = draws[-window:] if len(draws) >= window else draws
            if recent:
                momentum = sum(1 for d in recent if self._get_draw_numbers(d) and number in self._get_draw_numbers(d))
                features[f'momentum_{window}'] = momentum / len(recent)
            else:
                features[f'momentum_{window}'] = 0

        return features

    def _calculate_simple_stats(self, draws, number):
        """Version simplifiée des statistiques"""
        features = {}

        # Calculer seulement le gap actuel et moyen
        gaps = []
        last_appearance = -1

        # Limiter à 100 derniers tirages pour éviter les calculs longs
        recent_draws = draws[-100:] if len(draws) > 100 else draws

        for i, draw in enumerate(recent_draws):
            draw_numbers = self._get_draw_numbers(draw)
            if draw_numbers and number in draw_numbers:
                if last_appearance >= 0:
                    gaps.append(i - last_appearance)
                last_appearance = i

        if gaps:
            features['gap_mean'] = np.mean(gaps)
            features['gap_current'] = len(recent_draws) - last_appearance - 1 if last_appearance >= 0 else len(recent_draws)
        else:
            features['gap_mean'] = 0
            features['gap_current'] = len(recent_draws)

        return features

    def _calculate_simple_temporal(self, current_draw):
        """Version simplifiée des features temporelles"""
        features = {}

        if hasattr(current_draw, 'draw_date') and current_draw.draw_date:
            dt = current_draw.draw_date
            features['day_of_week'] = dt.weekday()
            features['hour'] = dt.hour
            features['month'] = dt.month
            features['is_weekend'] = 1 if dt.weekday() >= 5 else 0
        else:
            features['day_of_week'] = 0
            features['hour'] = 12
            features['month'] = 1
            features['is_weekend'] = 0

        return features

    def _calculate_statistical_features(self, draws, number):
        """Calcule des features statistiques avancées"""
        features = {}

        # Calculer les gaps
        gaps = []
        last_appearance = -1

        for i, draw in enumerate(draws):
            draw_numbers = self._get_draw_numbers(draw)
            if draw_numbers and number in draw_numbers:
                if last_appearance >= 0:
                    gaps.append(i - last_appearance)
                last_appearance = i

        if gaps:
            features['gap_mean'] = np.mean(gaps)
            features['gap_std'] = np.std(gaps)
            features['gap_skewness'] = self._calculate_skewness(gaps)
            features['gap_kurtosis'] = self._calculate_kurtosis(gaps)
            features['gap_cv'] = np.std(gaps) / max(0.001, np.mean(gaps))
            features['gap_current'] = len(draws) - last_appearance - 1
            features['gap_percentile'] = np.percentile(gaps, 75) if len(gaps) > 3 else 0
        else:
            features.update({
                'gap_mean': 0, 'gap_std': 0, 'gap_skewness': 0,
                'gap_kurtosis': 0, 'gap_cv': 0, 'gap_current': len(draws),
                'gap_percentile': 0
            })

        return features

    def _calculate_temporal_patterns(self, draws, number, current_draw):
        """Calcule des patterns temporels avancés"""
        features = {}

        # Features temporelles du tirage actuel
        if hasattr(current_draw, 'draw_date') and current_draw.draw_date:
            dt = current_draw.draw_date
            features['day_of_week'] = dt.weekday()
            features['hour'] = dt.hour
            features['month'] = dt.month
            features['day_of_month'] = dt.day
            features['week_of_year'] = dt.isocalendar()[1]
            features['is_weekend'] = 1 if dt.weekday() >= 5 else 0
            features['is_month_start'] = 1 if dt.day <= 7 else 0
            features['is_month_end'] = 1 if dt.day >= 24 else 0
        else:
            features.update({
                'day_of_week': 0, 'hour': 12, 'month': 1, 'day_of_month': 15,
                'week_of_year': 1, 'is_weekend': 0, 'is_month_start': 0, 'is_month_end': 0
            })

        # Patterns saisonniers
        appearances_by_month = [0] * 12
        appearances_by_dow = [0] * 7

        for draw in draws:
            draw_numbers = self._get_draw_numbers(draw)
            if draw_numbers and number in draw_numbers and hasattr(draw, 'draw_date') and draw.draw_date:
                appearances_by_month[draw.draw_date.month - 1] += 1
                appearances_by_dow[draw.draw_date.weekday()] += 1

        current_month = features['month'] - 1
        current_dow = features['day_of_week']

        features['seasonal_month_freq'] = appearances_by_month[current_month] / max(1, sum(appearances_by_month))
        features['seasonal_dow_freq'] = appearances_by_dow[current_dow] / max(1, sum(appearances_by_dow))

        return features

    def _calculate_entropy_features(self, draws, number):
        """Calcule des features d'entropie et de complexité"""
        features = {}

        # Séquence binaire des apparitions
        sequence = []
        for draw in draws[-100:]:  # Derniers 100 tirages
            draw_numbers = self._get_draw_numbers(draw)
            sequence.append(1 if draw_numbers and number in draw_numbers else 0)

        if len(sequence) > 10:
            # Entropie de Shannon
            p1 = sum(sequence) / len(sequence)
            p0 = 1 - p1
            if p1 > 0 and p0 > 0:
                features['shannon_entropy'] = -(p1 * np.log2(p1) + p0 * np.log2(p0))
            else:
                features['shannon_entropy'] = 0

            # Complexité de Lempel-Ziv (approximation)
            features['lz_complexity'] = self._approximate_lz_complexity(sequence)

            # Runs test (séquences consécutives)
            runs = self._calculate_runs(sequence)
            features['runs_count'] = len(runs)
            features['avg_run_length'] = np.mean([len(run) for run in runs]) if runs else 0

        else:
            features.update({
                'shannon_entropy': 0, 'lz_complexity': 0,
                'runs_count': 0, 'avg_run_length': 0
            })

        return features

    def _calculate_correlation_features(self, draws, number, current_numbers):
        """Calcule des features de corrélation avec autres numéros"""
        features = {}

        # Co-occurrence avec les numéros du tirage actuel
        cooccurrence_scores = []

        for other_num in current_numbers:
            if other_num != number:
                # Calculer la corrélation historique
                correlation = self._calculate_number_correlation(draws, number, other_num)
                cooccurrence_scores.append(correlation)

        if cooccurrence_scores:
            features['avg_cooccurrence'] = np.mean(cooccurrence_scores)
            features['max_cooccurrence'] = np.max(cooccurrence_scores)
            features['cooccurrence_strength'] = len([s for s in cooccurrence_scores if s > 0.1])
        else:
            features.update({
                'avg_cooccurrence': 0, 'max_cooccurrence': 0, 'cooccurrence_strength': 0
            })

        return features

    def _calculate_volatility_features(self, draws, number):
        """Calcule des features de volatilité"""
        features = {}

        # Volatilité des gaps
        gaps = []
        last_appearance = -1

        for i, draw in enumerate(draws):
            draw_numbers = self._get_draw_numbers(draw)
            if draw_numbers and number in draw_numbers:
                if last_appearance >= 0:
                    gaps.append(i - last_appearance)
                last_appearance = i

        if len(gaps) > 3:
            # Volatilité glissante
            window_volatilities = []
            for i in range(3, len(gaps)):
                window_gaps = gaps[i-3:i]
                window_volatilities.append(np.std(window_gaps))

            if window_volatilities:
                features['volatility_mean'] = np.mean(window_volatilities)
                features['volatility_trend'] = window_volatilities[-1] - window_volatilities[0] if len(window_volatilities) > 1 else 0
                features['volatility_current'] = window_volatilities[-1] if window_volatilities else 0
            else:
                features.update({'volatility_mean': 0, 'volatility_trend': 0, 'volatility_current': 0})
        else:
            features.update({'volatility_mean': 0, 'volatility_trend': 0, 'volatility_current': 0})

        return features

    def _calculate_skewness(self, data):
        """Calcule l'asymétrie (skewness)"""
        if len(data) < 3:
            return 0

        mean = np.mean(data)
        std = np.std(data)
        if std == 0:
            return 0

        skew = np.mean([(x - mean) ** 3 for x in data]) / (std ** 3)
        return skew

    def _calculate_kurtosis(self, data):
        """Calcule l'aplatissement (kurtosis)"""
        if len(data) < 4:
            return 0

        mean = np.mean(data)
        std = np.std(data)
        if std == 0:
            return 0

        kurt = np.mean([(x - mean) ** 4 for x in data]) / (std ** 4) - 3
        return kurt

    def _approximate_lz_complexity(self, sequence):
        """Approximation de la complexité de Lempel-Ziv"""
        if len(sequence) < 2:
            return 0

        complexity = 1
        i = 0

        while i < len(sequence) - 1:
            j = i + 1
            while j <= len(sequence):
                substring = sequence[i:j]
                if substring not in [sequence[k:k+len(substring)] for k in range(i)]:
                    break
                j += 1
            complexity += 1
            i = j - 1

        return complexity / len(sequence)

    def _calculate_runs(self, sequence):
        """Calcule les séquences consécutives (runs)"""
        if not sequence:
            return []

        runs = []
        current_run = [sequence[0]]

        for i in range(1, len(sequence)):
            if sequence[i] == sequence[i-1]:
                current_run.append(sequence[i])
            else:
                runs.append(current_run)
                current_run = [sequence[i]]

        runs.append(current_run)
        return runs

    def _calculate_number_correlation(self, draws, num1, num2):
        """Calcule la corrélation entre deux numéros"""
        appearances_1 = []
        appearances_2 = []

        for draw in draws[-200:]:  # Derniers 200 tirages
            draw_numbers = self._get_draw_numbers(draw)
            if draw_numbers:
                appearances_1.append(1 if num1 in draw_numbers else 0)
                appearances_2.append(1 if num2 in draw_numbers else 0)

        if len(appearances_1) > 10:
            correlation = np.corrcoef(appearances_1, appearances_2)[0, 1]
            return correlation if not np.isnan(correlation) else 0

        return 0

    def train_ultra_model(self, number, mode="fast"):
        """Entraîne un modèle ultra-optimisé pour un numéro spécifique (version rapide)"""
        try:
            print(f"🚀 Entraînement ultra-optimisé du numéro {number} (mode: {mode})")

            # Créer les caractéristiques ultra-avancées
            result = self.create_ultra_features(number)
            if not result:
                print(f"❌ Impossible de créer les caractéristiques pour le numéro {number}")
                return None

            X, y, feature_names = result
            print(f"✨ {len(feature_names)} caractéristiques créées pour {len(X)} échantillons")

            if len(X) < 30:  # Seuil réduit pour mode ultra-rapide
                print(f"⚠️ Pas assez de données pour le numéro {number} ({len(X)} échantillons)")
                return None

            # Diviser les données
            from sklearn.model_selection import train_test_split
            from sklearn.metrics import f1_score, accuracy_score
            from sklearn.feature_selection import SelectKBest, f_classif

            X_train, X_test, y_train, y_test = train_test_split(
                X, y, test_size=0.2, random_state=42, stratify=y
            )

            # Sélection des meilleures caractéristiques (réduite)
            k_features = min(10, X_train.shape[1])  # Encore moins de features
            selector = SelectKBest(score_func=f_classif, k=k_features)
            X_train_selected = selector.fit_transform(X_train, y_train)
            X_test_selected = selector.transform(X_test)

            print(f"📊 {k_features} meilleures caractéristiques sélectionnées")

            # Entraîner UN SEUL modèle pour aller plus vite
            if sklearn_available:
                from sklearn.ensemble import RandomForestClassifier
                model = RandomForestClassifier(
                    n_estimators=50,  # Très réduit pour vitesse
                    max_depth=8,      # Réduit
                    min_samples_split=5,
                    min_samples_leaf=3,
                    class_weight='balanced',
                    random_state=42,
                    n_jobs=-1
                )

                print("🌲 Entraînement Random Forest rapide...")
                model.fit(X_train_selected, y_train)
                y_pred = model.predict(X_test_selected)

                f1 = f1_score(y_test, y_pred, zero_division=0)
                accuracy = accuracy_score(y_test, y_pred)

                print(f"🎯 Random Forest - F1: {f1:.4f}, Précision: {accuracy:.4f}")

                best_model = {
                    'model': model,
                    'selector': selector,
                    'ensemble': False,
                    'model_type': 'random_forest',
                    'feature_names': feature_names,
                    'performance': {
                        'f1_score': f1,
                        'accuracy': accuracy,
                        'test_size': len(y_test)
                    }
                }

                # Sauvegarder le modèle
                self.models[number] = best_model

                print(f"✅ Modèle ultra-rapide créé pour le numéro {number}")
                return best_model
            else:
                print("❌ Scikit-learn non disponible")
                return None

        except Exception as e:
            print(f"❌ Erreur lors de l'entraînement du numéro {number}: {e}")
            import traceback
            traceback.print_exc()
            return None

    def predict_number_probability(self, number, current_features=None):
        """Prédit la probabilité qu'un numéro sorte au prochain tirage"""
        if number not in self.models:
            return 0.0

        try:
            model_data = self.models[number]

            # Si pas de caractéristiques fournies, créer à partir des données actuelles
            if current_features is None:
                # Utiliser les dernières données pour créer les caractéristiques
                draws = self.data_manager.draws
                if len(draws) < 100:
                    return 0.0

                # Créer les caractéristiques pour le dernier tirage
                features = self._create_current_features(number, draws)
                if not features:
                    return 0.0
            else:
                features = current_features

            # Appliquer la sélection de caractéristiques
            features_array = np.array(features).reshape(1, -1)
            features_selected = model_data['selector'].transform(features_array)

            # Faire la prédiction
            if model_data['ensemble']:
                # Prédiction d'ensemble
                probas = []
                for model in model_data['models'].values():
                    proba = model.predict_proba(features_selected)[0, 1]
                    probas.append(proba)
                probability = np.mean(probas)
            else:
                # Prédiction d'un seul modèle
                probability = model_data['model'].predict_proba(features_selected)[0, 1]

            return float(probability)

        except Exception as e:
            print(f"Erreur lors de la prédiction pour le numéro {number}: {e}")
            return 0.0

    def _create_current_features(self, number, draws):
        """Crée les caractéristiques pour le tirage actuel"""
        try:
            if len(draws) < 100:
                return None

            # Utiliser les mêmes méthodes que create_ultra_features mais pour le dernier état
            features = {}

            # Features de fréquence
            for window in [3, 5, 10, 20, 50]:
                recent_draws = draws[-window:] if len(draws) >= window else draws
                freq = sum(1 for d in recent_draws if self._get_draw_numbers(d) and number in self._get_draw_numbers(d))
                features[f'freq_{window}'] = freq / max(1, window)
                features[f'freq_normalized_{window}'] = freq / max(1, len(recent_draws))
                features[f'freq_deviation_{window}'] = (freq - window * 20/70) / max(1, np.sqrt(window))

            # Features de momentum
            momentum_features = self._calculate_advanced_momentum(draws, number)
            features.update(momentum_features)

            # Features statistiques
            statistical_features = self._calculate_statistical_features(draws, number)
            features.update(statistical_features)

            # Features temporelles (utiliser la date actuelle)
            from datetime import datetime
            current_time = datetime.now()
            temporal_features = {
                'day_of_week': current_time.weekday(),
                'hour': current_time.hour,
                'month': current_time.month,
                'day_of_month': current_time.day,
                'week_of_year': current_time.isocalendar()[1],
                'is_weekend': 1 if current_time.weekday() >= 5 else 0,
                'is_month_start': 1 if current_time.day <= 7 else 0,
                'is_month_end': 1 if current_time.day >= 24 else 0
            }
            features.update(temporal_features)

            # Features d'entropie
            entropy_features = self._calculate_entropy_features(draws, number)
            features.update(entropy_features)

            # Features de volatilité
            volatility_features = self._calculate_volatility_features(draws, number)
            features.update(volatility_features)

            return list(features.values())

        except Exception as e:
            print(f"Erreur lors de la création des caractéristiques actuelles: {e}")
            return None

    def get_ultra_predictions(self, count=10):
        """Génère des prédictions ultra-optimisées"""
        try:
            if not self.models:
                print("❌ Aucun modèle entraîné disponible")
                return []

            print(f"🎯 Génération de {count} prédictions ultra-optimisées...")

            # Calculer les probabilités pour tous les numéros entraînés
            probabilities = {}
            for number in self.models.keys():
                prob = self.predict_number_probability(number)
                if prob > 0:
                    probabilities[number] = prob

            if not probabilities:
                print("❌ Aucune probabilité calculée")
                return []

            # Trier par probabilité décroissante
            sorted_numbers = sorted(probabilities.items(), key=lambda x: x[1], reverse=True)

            # Prendre les meilleurs numéros
            predictions = []
            for number, prob in sorted_numbers[:count]:
                predictions.append({
                    'number': number,
                    'probability': prob,
                    'confidence': min(prob * 2, 1.0)  # Facteur de confiance
                })

            print(f"✅ {len(predictions)} prédictions générées")
            return predictions

        except Exception as e:
            print(f"❌ Erreur lors de la génération des prédictions: {e}")
            import traceback
            traceback.print_exc()
            return []

    def save_models(self, filepath):
        """Sauvegarde les modèles entraînés"""
        try:
            import pickle
            import os

            # Créer le répertoire si nécessaire
            os.makedirs(os.path.dirname(filepath), exist_ok=True)

            with open(filepath, 'wb') as f:
                pickle.dump(self.models, f)

            print(f"✅ Modèles sauvegardés dans {filepath}")
            return True

        except Exception as e:
            print(f"❌ Erreur lors de la sauvegarde: {e}")
            return False

    def load_models(self, filepath):
        """Charge les modèles sauvegardés"""
        try:
            import pickle
            import os

            if not os.path.exists(filepath):
                print(f"⚠️ Fichier de modèles non trouvé: {filepath}")
                return False

            with open(filepath, 'rb') as f:
                self.models = pickle.load(f)

            print(f"✅ Modèles chargés depuis {filepath}")
            print(f"📊 {len(self.models)} modèles disponibles pour les numéros: {list(self.models.keys())}")
            return True

        except Exception as e:
            print(f"❌ Erreur lors du chargement: {e}")
            return False