#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Test simple de l'optimiseur ultra
"""

import os
import sys
import warnings

# Suppression des warnings
warnings.filterwarnings('ignore')
os.environ['TF_CPP_MIN_LOG_LEVEL'] = '3'

def simple_test():
    """Test simple et rapide"""
    print("🧪 TEST SIMPLE DE L'OPTIMISEUR ULTRA")
    print("=" * 50)
    
    try:
        # Test d'import
        print("📦 Test d'import...")
        from keno_ultra_optimizer import KenoUltraOptimizer
        print("✅ Import réussi")
        
        # Test d'initialisation basique
        print("🔧 Test d'initialisation...")
        
        # Mock simple pour les tests
        class MockDataManager:
            def __init__(self):
                self.draws = []
                # Créer quelques tirages de test
                from datetime import datetime, timedelta
                for i in range(500):  # Moins de données pour test rapide
                    draw = MockDraw()
                    draw.draw_date = datetime.now() - timedelta(days=i)
                    draw.draw_numbers = [1, 5, 10, 15, 20, 25, 30, 35, 40, 45, 50, 55, 60, 65, 70, 2, 7, 12, 17, 22]
                    # Varier les numéros pour avoir des patterns
                    if i % 10 == 0:
                        draw.draw_numbers[0] = 7  # Le numéro 7 apparaît parfois
                    self.draws.append(draw)
            
            def get_draws_count(self):
                return len(self.draws)
        
        class MockDraw:
            def __init__(self):
                self.draw_date = None
                self.draw_numbers = []
        
        class MockAnalyzer:
            def __init__(self, data_manager):
                self.data_manager = data_manager
        
        data_manager = MockDataManager()
        analyzer = MockAnalyzer(data_manager)
        
        print(f"📊 {data_manager.get_draws_count()} tirages de test créés")
        
        # Créer l'optimiseur
        ultra_optimizer = KenoUltraOptimizer(data_manager, analyzer)
        print("✅ Optimiseur créé")
        
        # Test de création de caractéristiques
        print("🧠 Test de création de caractéristiques...")
        result = ultra_optimizer.create_ultra_features(7, lookback_days=None, max_samples=200)
        
        if result:
            X, y, feature_names = result
            print(f"✅ {len(feature_names)} caractéristiques créées pour {len(X)} échantillons")
            print(f"📊 Exemples: {feature_names[:3]}")
            
            # Test d'entraînement simple
            print("🤖 Test d'entraînement...")
            model_result = ultra_optimizer.train_ultra_model(7, mode="fast")
            
            if model_result:
                perf = model_result['performance']
                print(f"✅ Modèle entraîné")
                print(f"🎯 F1-score: {perf['f1_score']:.4f}")
                print(f"📊 Précision: {perf['accuracy']:.4f}")
                
                # Test de prédiction
                print("🔮 Test de prédiction...")
                prob = ultra_optimizer.predict_number_probability(7)
                print(f"✅ Probabilité: {prob:.4f}")
                
                print("\n🎉 TEST SIMPLE RÉUSSI ! 🎉")
                return True
            else:
                print("❌ Échec d'entraînement")
                return False
        else:
            print("❌ Échec de création des caractéristiques")
            return False
            
    except Exception as e:
        print(f"❌ ERREUR: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = simple_test()
    if success:
        print("✨ L'optimiseur ultra fonctionne correctement !")
    else:
        print("🔧 Il y a un problème à résoudre")
