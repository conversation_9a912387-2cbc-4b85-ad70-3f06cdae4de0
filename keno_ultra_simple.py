#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Système ULTRA-OPTIMISÉ simplifié pour éviter les conflits de parallélisation
Version stable qui exploite efficacement vos ressources
"""

import os
import warnings
import numpy as np

# Configuration optimisée mais stable
os.environ['OMP_NUM_THREADS'] = '8'  # Limiter pour éviter les conflits
os.environ['MKL_NUM_THREADS'] = '8'
warnings.filterwarnings('ignore')

class KenoUltraSimple:
    """Système ultra-optimisé simplifié et stable"""
    
    def __init__(self, data_manager, analyzer):
        self.data_manager = data_manager
        self.analyzer = analyzer
        
        print(f"🚀 Système ULTRA-OPTIMISÉ SIMPLIFIÉ initialisé")
        print(f"💻 Configuration stable pour éviter les conflits")
        print(f"⚡ Optimisation des modèles avec 8 cœurs")
    
    def create_ultra_features_sequential(self, numbers_list):
        """Crée les caractéristiques ultra-avancées de manière séquentielle mais optimisée"""
        
        print(f"🚀 Création ultra-optimisée pour {len(numbers_list)} numéros")
        
        results = {}
        
        for i, number in enumerate(numbers_list):
            print(f"\n--- Traitement ULTRA du numéro {number} ({i+1}/{len(numbers_list)}) ---")
            
            try:
                from keno_ultra_optimizer import KenoUltraOptimizer
                optimizer = KenoUltraOptimizer(self.data_manager, self.analyzer)
                result = optimizer.create_ultra_features(number)
                
                if result:
                    X, y, feature_names = result
                    results[number] = {
                        'number': number,
                        'X': X,
                        'y': y,
                        'feature_names': feature_names,
                        'success': True,
                        'samples': len(X),
                        'features': len(feature_names)
                    }
                    print(f"✅ Numéro {number}: {len(feature_names)} caractéristiques ultra-avancées, {len(X)} échantillons")
                else:
                    results[number] = {
                        'number': number,
                        'success': False,
                        'error': 'Pas assez de données'
                    }
                    print(f"❌ Numéro {number}: Pas assez de données")
                    
            except Exception as e:
                results[number] = {
                    'number': number,
                    'success': False,
                    'error': str(e)
                }
                print(f"💥 Erreur numéro {number}: {e}")
        
        successful = len([r for r in results.values() if r['success']])
        print(f"\n🎯 Création terminée: {successful} succès sur {len(numbers_list)}")
        return results
    
    def train_models_optimized(self, features_results):
        """Entraîne les modèles de manière optimisée mais stable"""
        
        print(f"\n🚀 Entraînement ultra-optimisé des modèles")
        
        training_results = {}
        valid_data = [(num, res) for num, res in features_results.items() if res['success']]
        
        if not valid_data:
            print("❌ Aucune donnée valide pour l'entraînement")
            return training_results
        
        for i, (number, result) in enumerate(valid_data):
            print(f"\n--- Entraînement ULTRA du numéro {number} ({i+1}/{len(valid_data)}) ---")
            
            try:
                X, y = result['X'], result['y']
                
                if len(X) < 50:
                    training_results[number] = {'number': number, 'success': False, 'error': 'Pas assez de données'}
                    print(f"❌ Numéro {number}: Pas assez de données")
                    continue
                
                # Imports pour l'entraînement
                from sklearn.ensemble import RandomForestClassifier
                from sklearn.model_selection import train_test_split
                from sklearn.metrics import f1_score, accuracy_score, precision_score, recall_score
                from sklearn.feature_selection import SelectKBest, f_classif
                
                # Division des données
                X_train, X_test, y_train, y_test = train_test_split(
                    X, y, test_size=0.2, random_state=42, stratify=y
                )
                
                # Sélection des meilleures caractéristiques
                k_best = min(30, X_train.shape[1])
                selector = SelectKBest(score_func=f_classif, k=k_best)
                X_train_selected = selector.fit_transform(X_train, y_train)
                X_test_selected = selector.transform(X_test)
                
                print(f"  📊 {k_best} meilleures caractéristiques sélectionnées")
                
                # Modèle ultra-optimisé mais stable
                model = RandomForestClassifier(
                    n_estimators=500,  # Plus d'arbres pour de meilleures performances
                    max_depth=20,
                    min_samples_split=3,
                    min_samples_leaf=1,
                    class_weight='balanced',
                    n_jobs=8,  # Utiliser 8 cœurs de manière stable
                    random_state=42,
                    verbose=0
                )
                
                print(f"  🔥 Entraînement avec 500 arbres sur 8 cœurs...")
                
                # Entraînement intensif
                model.fit(X_train_selected, y_train)
                
                # Prédictions
                y_pred = model.predict(X_test_selected)
                
                # Métriques complètes
                f1 = f1_score(y_test, y_pred, zero_division=0)
                accuracy = accuracy_score(y_test, y_pred)
                precision = precision_score(y_test, y_pred, zero_division=0)
                recall = recall_score(y_test, y_pred, zero_division=0)
                
                training_results[number] = {
                    'number': number,
                    'success': True,
                    'f1_score': f1,
                    'accuracy': accuracy,
                    'precision': precision,
                    'recall': recall,
                    'n_features': k_best,
                    'n_samples': len(X_train)
                }
                
                print(f"  🎯 F1-score: {f1:.4f} | Précision: {accuracy:.4f}")
                print(f"  ✅ Numéro {number} entraîné avec succès")
                
            except Exception as e:
                training_results[number] = {'number': number, 'success': False, 'error': str(e)}
                print(f"  💥 Erreur entraînement {number}: {e}")
        
        return training_results
    
    def run_ultra_simple_training(self, mode="ultra_fast"):
        """Lance l'entraînement ultra-optimisé simplifié"""
        
        print(f"🚀 DÉMARRAGE ENTRAÎNEMENT ULTRA-OPTIMISÉ SIMPLIFIÉ")
        print(f"💻 Version stable pour éviter les conflits")
        print(f"⚡ Optimisation avec 8 cœurs")
        
        # Déterminer les numéros selon le mode
        if mode == "ultra_fast":
            numbers_to_train = [7, 21, 35, 49, 63]
            mode_name = "ULTRA-RAPIDE"
        elif mode == "fast":
            numbers_to_train = [1, 7, 14, 21, 28, 35, 42, 49, 56, 63, 70, 3, 17, 31, 45]
            mode_name = "RAPIDE"
        else:  # complete
            numbers_to_train = list(range(1, 71))
            mode_name = "COMPLET"
        
        print(f"🎯 Mode {mode_name}: {len(numbers_to_train)} numéros")
        
        # Phase 1: Création des caractéristiques
        print(f"\n🔥 PHASE 1: CRÉATION DES CARACTÉRISTIQUES ULTRA-AVANCÉES")
        features_results = self.create_ultra_features_sequential(numbers_to_train)
        
        # Phase 2: Entraînement des modèles
        print(f"\n🔥 PHASE 2: ENTRAÎNEMENT ULTRA-OPTIMISÉ DES MODÈLES")
        training_results = self.train_models_optimized(features_results)
        
        # Résultats finaux
        successful_models = [r for r in training_results.values() if r['success']]
        
        if successful_models:
            avg_f1 = np.mean([r['f1_score'] for r in successful_models])
            avg_accuracy = np.mean([r['accuracy'] for r in successful_models])
            
            print(f"\n🎉 ENTRAÎNEMENT ULTRA-OPTIMISÉ TERMINÉ !")
            print(f"✅ Modèles entraînés: {len(successful_models)}/{len(numbers_to_train)}")
            print(f"🎯 F1-score moyen: {avg_f1:.4f}")
            print(f"📊 Précision moyenne: {avg_accuracy:.4f}")
            print(f"💻 Optimisation: 8 cœurs utilisés efficacement")
            
            return {
                'success': True,
                'trained_models': len(successful_models),
                'total_numbers': len(numbers_to_train),
                'avg_f1': avg_f1,
                'avg_accuracy': avg_accuracy,
                'results': training_results
            }
        else:
            print(f"\n❌ ÉCHEC: Aucun modèle entraîné avec succès")
            return {'success': False, 'results': training_results}

def test_ultra_simple():
    """Test du système ultra-optimisé simplifié"""
    
    print("🚀 TEST DU SYSTÈME ULTRA-OPTIMISÉ SIMPLIFIÉ")
    
    try:
        # Importer les modules nécessaires
        from keno_data import KenoDataManager
        from keno_analyzer import KenoAnalyzer
        
        # Créer les instances
        data_manager = KenoDataManager()
        analyzer = KenoAnalyzer(data_manager)
        
        # Charger les données
        data_dir = os.path.join(os.path.dirname(__file__), 'data')
        if os.path.exists(data_dir):
            data_files = [f for f in os.listdir(data_dir) if f.endswith('.keno')]
            if data_files:
                latest_file = max(data_files, key=lambda f: os.path.getmtime(os.path.join(data_dir, f)))
                file_path = os.path.join(data_dir, latest_file)
                
                print(f"📊 Chargement: {file_path}")
                success = data_manager.load_database(file_path)
                
                if success:
                    print(f"✅ {data_manager.get_draws_count()} tirages chargés")
                    
                    # Créer le système ultra-optimisé simplifié
                    ultra_simple = KenoUltraSimple(data_manager, analyzer)
                    
                    # Lancer l'entraînement ultra-optimisé
                    result = ultra_simple.run_ultra_simple_training("ultra_fast")
                    
                    if result['success']:
                        print(f"\n🎉 SUCCÈS ULTRA-OPTIMISÉ SIMPLIFIÉ !")
                        return True
                    else:
                        print(f"\n❌ Échec de l'entraînement")
                        return False
                else:
                    print("❌ Échec du chargement des données")
                    return False
            else:
                print("❌ Aucun fichier de données trouvé")
                return False
        else:
            print("❌ Répertoire de données non trouvé")
            return False
            
    except Exception as e:
        print(f"❌ Erreur: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    test_ultra_simple()
