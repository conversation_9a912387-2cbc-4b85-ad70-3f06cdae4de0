import tkinter as tk
from tkinter import ttk, filedialog, messagebox
from datetime import datetime
# Import matplotlib de manière conditionnelle pour éviter les erreurs
try:
    import matplotlib.pyplot as plt
    from matplotlib.backends.backend_tkagg import FigureCanvasTkAgg
    matplotlib_available = True
except ImportError:
    plt = None
    FigureCanvasTkAgg = None
    matplotlib_available = False
# Import numpy de manière conditionnelle pour éviter les erreurs
try:
    import numpy
    numpy_available = True
except ImportError:
    numpy = None
    numpy_available = False

# Import pandas de manière conditionnelle
try:
    import pandas
    pandas_available = True
except ImportError:
    pandas = None
    pandas_available = False

# Import scikit-learn de manière conditionnelle
try:
    import sklearn
    sklearn_available = True
except ImportError:
    sklearn = None
    sklearn_available = False
import json
import csv
import os
import threading
import time
import queue
import tkinter as tk
from tkinter import ttk, messagebox, filedialog

# Le module de vérification des dépendances est importé dynamiquement dans setup_auto_save


# Import du système ULTRA-OPTIMISÉ
try:
    from keno_ultra_optimizer import KenoUltraOptimizer
    from keno_ultra_integration import integrate_ultra_system, run_ultra_training
    ultra_system_available = True
    print("✓ Système ULTRA-OPTIMISÉ disponible")
except ImportError as e:
    ultra_system_available = False
    print(f"✗ Système ULTRA-OPTIMISÉ non disponible: {e}")

from keno_data import KenoDataManager
from keno_analyzer import KenoAnalyzer

# Importer le module des probabilités
try:
    from keno_probabilities import setup_probabilities_tab
    probabilities_module_available = True
except ImportError:
    print("Module des probabilités non disponible.")
    probabilities_module_available = False

# Importer les fonctions de sauvegarde et chargement des données d'apprentissage
try:
    from save_learning_data import save_learning_data, load_learning_data
    learning_data_module_available = True
except ImportError:
    print("Module de sauvegarde des données d'apprentissage non disponible.")
    learning_data_module_available = False


# === SYSTÈME ULTRA-OPTIMISÉ ===
try:
    from keno_ultra_optimizer import KenoUltraOptimizer
    from keno_ultra_integration import integrate_ultra_system, run_ultra_training
    ultra_system_available = True
    print("✓ Système ULTRA-OPTIMISÉ disponible")
except ImportError as e:
    ultra_system_available = False
    print(f"✗ Système ULTRA-OPTIMISÉ non disponible: {e}")

class KenoGUI(tk.Tk):
    def __init__(self):
        # Initialisation de l'interface graphique
        super().__init__()

        # Initialiser les attributs
        self.data_manager = KenoDataManager()
        self.analyzer = KenoAnalyzer(self.data_manager)
        self.progress_bar = None
        self.prediction_text = None
        self.prediction_frame = None
        self.prediction_tab = None
        self.font_size = 14  # Taille de police par défaut augmentée

        # Initialiser le gestionnaire de feedback
        self.feedback_manager = None
        try:
            from keno_feedback import KenoFeedback
            self.feedback_manager = KenoFeedback(self.data_manager)
            print("Gestionnaire de feedback initialisé avec succès")
        except ImportError:
            print("Module de feedback non disponible.")

        # Définir les polices
        self.text_font = ('Helvetica', self.font_size)
        self.title_font = ('Helvetica', self.font_size+4, 'bold')


        # Vérifier si l'analyse avancée est disponible
        try:
            from keno_advanced_analyzer import KenoAdvancedAnalyzer
            self.advanced_analysis_available = True
        except ImportError:
            self.advanced_analysis_available = False

        # Configurer l'interface
        self.title("Keno Analyzer")
        self.geometry("1200x800")
        self.minsize(800, 600)

        # Créer un verrou pour les opérations threadées
        self.thread_lock = threading.Lock()

        # File d'attente pour la communication entre threads
        self.task_queue = queue.Queue()

        # Variables de configuration
        self.max_number_var = tk.IntVar(value=70)
        self.numbers_per_draw_var = tk.IntVar(value=20)
        self.prediction_count_var = tk.IntVar(value=10)
        self.prediction_method_var = tk.StringVar(value="combined")

        # Variables pour l'importation
        self.csv_path_var = tk.StringVar()
        self.date_format_var = tk.StringVar(value="%d/%m/%Y %H:%M:%S")  # Format français par défaut
        self.date_column_var = tk.IntVar(value=0)
        self.numbers_start_column_var = tk.IntVar(value=1)
        self.has_header_var = tk.BooleanVar(value=True)
        self.delimiter_var = tk.StringVar(value=";")  # Point-virgule par défaut pour les fichiers français
        self.french_date_var = tk.BooleanVar(value=True)  # Activé par défaut
        self.time_column_var = tk.IntVar(value=2)  # Par défaut, colonne 2 pour le format FDJ
        self.combine_date_time_var = tk.BooleanVar(value=True)  # Activé par défaut
        self.id_column_var = tk.IntVar(value=0)  # Par défaut, colonne 0 pour le format FDJ
        self.clear_existing_var = tk.BooleanVar(value=True)
        self.import_instruction_var = tk.StringVar(value="Cliquez sur 'Parcourir' pour sélectionner un fichier, puis sur 'Importer' pour valider l'importation.")

        # Configuration pour permettre à la fenêtre de s'étendre
        self.columnconfigure(0, weight=1)
        self.rowconfigure(0, weight=1)

        # Créer les widgets (une seule fois)
        self.create_widgets()

        # Activer le téléchargement automatique si la méthode existe
        if hasattr(self, 'enable_auto_download'):
            self.enable_auto_download()

        # Charger les données d'apprentissage si disponibles (sans lancer l'auto-amélioration)
        # Désactivé pour éviter le démarrage automatique de l'auto-amélioration
        # Le chargement des données d'apprentissage peut être fait manuellement via le menu
        print("Chargement automatique des données d'apprentissage désactivé")
        print("Utilisez le menu Outils > Charger données d'apprentissage pour les charger manuellement")

        # Charger automatiquement les modèles ultra-optimisés si disponibles
        self.ultra_optimizer = None
        self._load_ultra_models_if_available()

    def create_widgets(self):
        # Ajout d'un thème sombre moderne
        self.style = ttk.Style()
        self.style.theme_use('clam')

        # Configuration des couleurs
        self.configure(bg='#2c3e50')

        # Configuration des styles pour les frames
        self.style.configure('Custom.TFrame', background='#2c3e50')
        self.style.configure('TLabelframe', background='#2c3e50', foreground='white')
        self.style.configure('TLabelframe.Label', background='#2c3e50', foreground='white', font=('Helvetica', self.font_size, 'bold'))

        # Configuration des styles pour les boutons
        self.style.configure('Custom.TButton',
                            background='#3498db',
                            foreground='white',
                            padding=10,
                            font=('Helvetica', 11, 'bold'))
        self.style.configure('Success.TButton',
                            background='#27ae60',
                            foreground='white')
        self.style.configure('Warning.TButton',
                            background='#f39c12',
                            foreground='white')

        # Style pour les grands boutons
        self.style.configure('BigButton.TButton',
                           background='#3498db',
                           foreground='white',
                           padding=15,
                           font=('Helvetica', self.font_size+2, 'bold'))

        # Style pour les boutons d'action
        self.style.configure('Action.TButton',
                           background='#e74c3c',
                           foreground='white',
                           padding=10,
                           font=('Helvetica', self.font_size, 'bold'))

        # Style pour les boutons de mise en évidence
        self.style.configure('Highlight.TButton',
                           background='#9b59b6',
                           foreground='white',
                           padding=10,
                           font=('Helvetica', self.font_size, 'bold'))

        # Style pour les éléments standard
        self.style.configure('TButton', padding=10, font=('Helvetica', self.font_size))
        self.style.configure('TLabel', background='#2c3e50', foreground='white', font=('Helvetica', self.font_size))
        self.style.configure('TLabelframe', background='#2c3e50', foreground='white', font=('Helvetica', self.font_size))
        self.style.configure('TLabelframe.Label', background='#2c3e50', foreground='white', font=('Helvetica', self.font_size, 'bold'))
        self.style.configure('TEntry', font=('Helvetica', self.font_size))
        self.style.configure('TSpinbox', font=('Helvetica', self.font_size))
        self.style.configure('TCombobox', font=('Helvetica', self.font_size))

        # Style pour mettre en évidence les boutons d'importation
        self.style.configure('Highlight.TButton', padding=10, font=('Helvetica', self.font_size, 'bold'))
        self.style.map('Highlight.TButton', background=[('!active', '#27ae60'), ('active', '#2ecc71')],
                  foreground=[('!active', 'white'), ('active', 'white')])

        # Style pour les gros boutons d'action
        self.style.configure('BigButton.TButton', padding=12, font=('Helvetica', self.font_size+2, 'bold'))
        self.style.map('BigButton.TButton', background=[('!active', '#3498db'), ('active', '#2980b9')],
                  foreground=[('!active', 'white'), ('active', 'white')])

        # Ajout d'une barre de progression
        self.progress_var = tk.DoubleVar()
        self.progress_bar = ttk.Progressbar(self,
                                          variable=self.progress_var,
                                          mode='determinate')
        self.progress_bar.pack(fill=tk.X, padx=10, pady=5)

        # Créer un menu principal
        menubar = tk.Menu(self)
        self.config(menu=menubar)

        # Menu Fichier
        file_menu = tk.Menu(menubar, tearoff=0)
        menubar.add_cascade(label="Fichier", menu=file_menu)
        file_menu.add_command(label="Importer CSV", command=self.browse_csv)
        file_menu.add_command(label="Importation multiple", command=self.import_multiple_csv)
        file_menu.add_separator()
        file_menu.add_command(label="Télécharger données", command=self.download_data)
        file_menu.add_separator()
        file_menu.add_command(label="Sauvegarder BDD", command=self.save_database)
        file_menu.add_command(label="Charger BDD", command=self.load_database)
        file_menu.add_separator()
        file_menu.add_command(label="Quitter", command=self.quit)

        # Menu Configuration
        config_menu = tk.Menu(menubar, tearoff=0)
        menubar.add_cascade(label="Configuration", menu=config_menu)
        config_menu.add_command(label="Paramètres de configuration", command=self.open_config_window)
        config_menu.add_command(label="Options d'importation avancées", command=self.open_advanced_options)

        # Ajouter l'option de configuration avancée si l'analyseur avancé est disponible
        if self.advanced_analysis_available:
            config_menu.add_command(label="Configuration avancée (GPU/CPU)", command=self.open_advanced_config)

        config_menu.add_separator()
        config_menu.add_command(label="Sauvegarder configuration", command=self.save_config)
        config_menu.add_command(label="Charger configuration", command=self.load_config)

        # Menu Outils
        tools_menu = tk.Menu(menubar, tearoff=0)
        menubar.add_cascade(label="Outils", menu=tools_menu)
        tools_menu.add_command(label="Mettre à jour statistiques", command=self.update_stats)
        tools_menu.add_command(label="Effacer données", command=self.clear_data)

        # Ajouter les options pour les données d'apprentissage si le module est disponible
        if learning_data_module_available:
            tools_menu.add_separator()
            tools_menu.add_command(label="Sauvegarder données d'apprentissage", command=self.save_learning_data)
            tools_menu.add_command(label="Charger données d'apprentissage", command=self.load_learning_data)

        # Frame principale avec un notebook (onglets)
        main_frame = ttk.Frame(self, padding="10")
        main_frame.pack(fill=tk.BOTH, expand=True)

        # Créer un notebook pour les onglets
        notebook = ttk.Notebook(main_frame)
        notebook.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)

        # Onglet Importation
        import_tab = ttk.Frame(notebook)
        notebook.add(import_tab, text="Importation")

        # Contenu de l'onglet Importation
        import_frame = tk.LabelFrame(import_tab, text="Importation de données", padx=10, pady=10)
        import_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)

        # Chemin du fichier
        path_frame = tk.Frame(import_frame)
        path_frame.pack(fill=tk.X, pady=10)

        # Utiliser la variable csv_path_var déjà définie
        tk.Label(path_frame, text="Fichier CSV:").pack(side=tk.LEFT, padx=5)
        self.csv_path_entry = tk.Entry(path_frame, textvariable=self.csv_path_var, width=50)
        self.csv_path_entry.pack(side=tk.LEFT, fill=tk.X, expand=True, padx=5)

        # Boutons d'importation
        button_frame = tk.Frame(import_frame)
        button_frame.pack(fill=tk.X, pady=10)

        tk.Button(button_frame, text="Parcourir", command=self.browse_csv,
                 bg='#3498db', fg='white', width=15).pack(side=tk.LEFT, padx=10)
        tk.Button(button_frame, text="Importer", command=self.import_csv,
                 bg='#3498db', fg='white', width=15).pack(side=tk.LEFT, padx=10)
        tk.Button(button_frame, text="Importation multiple", command=self.import_multiple_csv,
                 bg='#3498db', fg='white', width=15).pack(side=tk.LEFT, padx=10)
        tk.Button(button_frame, text="Options avancées", command=self.open_advanced_options,
                 bg='#3498db', fg='white', width=15).pack(side=tk.LEFT, padx=10)

        # Onglet Prédiction
        prediction_tab = ttk.Frame(notebook)
        notebook.add(prediction_tab, text="Prédiction")

        # Contenu de l'onglet Prédiction
        prediction_frame = tk.LabelFrame(prediction_tab, text="Génération de prédictions", padx=10, pady=10)
        prediction_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)

        # Options de prédiction - Interface améliorée
        options_frame = tk.Frame(prediction_frame, bg='#2c3e50')
        options_frame.pack(fill=tk.X, pady=15)

        # Titre de la section
        title_label = tk.Label(options_frame, text="Paramètres de prédiction", font=self.title_font, bg='#2c3e50', fg='white')
        title_label.pack(pady=10)

        # Conteneur pour les options
        params_frame = tk.Frame(options_frame, bg='#2c3e50')
        params_frame.pack(fill=tk.X, padx=20, pady=10)

        # Méthode de prédiction
        method_label = tk.Label(params_frame, text="Méthode de prédiction:", font=self.text_font, bg='#2c3e50', fg='white')
        method_label.grid(row=0, column=0, sticky='w', padx=10, pady=10)

        # Créer le menu déroulant avec les méthodes disponibles
        prediction_methods = ["auto", "combined", "frequency", "pattern", "random"]
        if self.advanced_analysis_available:
            prediction_methods.extend(["ml", "patterns", "series", "advanced"])

        # Ajouter l'apprentissage par renforcement si disponible
        if hasattr(self, 'feedback_manager') and self.feedback_manager and hasattr(self.feedback_manager, 'rl_agent') and self.feedback_manager.rl_agent:
            prediction_methods.append("reinforcement_learning")

        # Utiliser la variable existante prediction_method_var
        self.prediction_method_var.set("auto")  # Valeur par défaut
        method_dropdown = ttk.Combobox(params_frame, textvariable=self.prediction_method_var,
                                      values=prediction_methods, width=15, font=self.text_font)
        method_dropdown.grid(row=0, column=1, padx=10, pady=10)

        # Nombre de numéros à prédire
        count_label = tk.Label(params_frame, text="Nombre de numéros:", font=self.text_font, bg='#2c3e50', fg='white')
        count_label.grid(row=1, column=0, sticky='w', padx=10, pady=10)

        # Utiliser la variable existante prediction_count_var
        self.prediction_count_var.set(10)  # Valeur par défaut
        num_entry = ttk.Spinbox(params_frame, from_=1, to=20, textvariable=self.prediction_count_var,
                               width=5, font=self.text_font)
        num_entry.grid(row=1, column=1, padx=10, pady=10)

        # Boutons de prédiction
        pred_button_frame = tk.Frame(prediction_frame, bg='#2c3e50')
        pred_button_frame.pack(fill=tk.X, pady=15)

        # Utiliser des boutons ttk avec style amélioré
        generate_btn = ttk.Button(pred_button_frame, text="Générer prédiction",
                                command=self.generate_prediction, style='BigButton.TButton')
        generate_btn.pack(side=tk.LEFT, padx=15, pady=10, expand=True, fill=tk.X)

        export_btn = ttk.Button(pred_button_frame, text="Exporter prédiction",
                              command=self.export_prediction, style='BigButton.TButton')
        export_btn.pack(side=tk.LEFT, padx=15, pady=10, expand=True, fill=tk.X)

        # Bouton pour saisir les résultats réels
        feedback_btn = ttk.Button(pred_button_frame, text="Saisir résultats réels",
                               command=self.show_feedback_dialog, style='BigButton.TButton')
        feedback_btn.pack(side=tk.LEFT, padx=15, pady=10, expand=True, fill=tk.X)

        # Boutons secondaires
        secondary_btn_frame = tk.Frame(prediction_frame, bg='#2c3e50')
        secondary_btn_frame.pack(fill=tk.X, pady=5)

        params_btn = ttk.Button(secondary_btn_frame, text="Paramètres",
                              command=self.open_config_window, style='TButton')
        params_btn.pack(side=tk.LEFT, padx=15, pady=5, expand=True, fill=tk.X)

        # Bouton d'auto-amélioration plus visible
        improve_frame = tk.Frame(prediction_frame, bg='#2c3e50')
        improve_frame.pack(fill=tk.X, pady=15)

        # Titre explicatif
        improve_label = tk.Label(improve_frame, text="Auto-amélioration des modèles de prédiction",
                               font=self.title_font, bg='#2c3e50', fg='white')
        improve_label.pack(pady=5)

        # Description
        description = tk.Label(improve_frame,
                             text="Cette fonction permet d'améliorer automatiquement les modèles de prédiction en analysant les données existantes.",
                             font=self.text_font, bg='#2c3e50', fg='white', wraplength=600)
        description.pack(pady=5)

        # Grand bouton d'auto-amélioration
        improve_btn = ttk.Button(improve_frame, text="LANCER L'AUTO-AMÉLIORATION",
                               command=self.show_auto_improve_dialog, style='BigButton.TButton')
        improve_btn.pack(pady=10, padx=50, ipady=10)

        # Zone de texte pour les prédictions - Améliorée
        prediction_text_frame = tk.LabelFrame(prediction_frame, text="Résultats de la prédiction",
                                           bg='#2c3e50', fg='white', font=self.title_font)
        prediction_text_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=15)

        # Scrollbar pour la zone de texte
        scrollbar = ttk.Scrollbar(prediction_text_frame)
        scrollbar.pack(side=tk.RIGHT, fill=tk.Y)

        # Zone de texte plus grande et plus lisible
        self.prediction_text = tk.Text(prediction_text_frame, height=20, width=80,
                                    font=('Courier New', self.font_size+2),
                                    bg='#f5f5f5', fg='#333333',
                                    yscrollcommand=scrollbar.set)
        self.prediction_text.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        scrollbar.config(command=self.prediction_text.yview)

        # Onglet Statistiques
        stats_tab = ttk.Frame(notebook)
        notebook.add(stats_tab, text="Statistiques")

        # Contenu de l'onglet Statistiques
        stats_frame = tk.LabelFrame(stats_tab, text="Statistiques des tirages", padx=10, pady=10)
        stats_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)

        # Onglet Probabilités et Gains
        if probabilities_module_available:
            proba_tab = ttk.Frame(notebook)
            notebook.add(proba_tab, text="Probabilités & Gains")

            # Configurer l'onglet des probabilités
            setup_probabilities_tab(proba_tab, font_size=self.font_size)

        # Bouton de mise à jour des statistiques
        tk.Button(stats_frame, text="Mettre à jour les statistiques", command=self.update_stats,
                 bg='#3498db', fg='white', width=25).pack(pady=10)

        # Créer un notebook pour les statistiques
        stats_notebook = ttk.Notebook(stats_frame)
        stats_notebook.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)

        # Onglet texte
        text_tab = ttk.Frame(stats_notebook)
        stats_notebook.add(text_tab, text="Texte")

        self.stats_text = tk.Text(text_tab, height=15, width=80)
        self.stats_text.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)

        # Onglet graphique (si matplotlib est disponible)
        if matplotlib_available:
            graph_tab = ttk.Frame(stats_notebook)
            stats_notebook.add(graph_tab, text="Graphique")

            # Créer un canevas pour le graphique
            try:
                self.figure, self.ax = plt.subplots(figsize=(8, 4))
                self.canvas = FigureCanvasTkAgg(self.figure, graph_tab)
                self.canvas.get_tk_widget().pack(fill=tk.BOTH, expand=True)
            except Exception as e:
                # En cas d'erreur, afficher un message
                error_label = ttk.Label(graph_tab, text="Erreur lors de la création du graphique:\n{}".format(e))
                error_label.pack(pady=20)
        else:
            # Informer l'utilisateur que matplotlib n'est pas disponible
            self.stats_text.insert(tk.END, "\nLes graphiques ne sont pas disponibles.\nVeuillez installer matplotlib pour activer cette fonctionnalité.")

        # Fin de la création des widgets
        self.progress_bar = None

    def open_advanced_config(self):
        """Ouvre la fenêtre de configuration avancée pour GPU/CPU"""
        # Vérifier que l'analyseur avancé est disponible
        if not self.advanced_analysis_available or not hasattr(self.analyzer, 'advanced_analyzer'):
            messagebox.showwarning("Configuration avancée", "L'analyseur avancé n'est pas disponible.")
            return

        # Importer le module de configuration avancée
        try:
            from keno_config_dialog import KenoConfigDialog
            # Créer la fenêtre de configuration
            config_dialog = KenoConfigDialog(self, self.analyzer.advanced_analyzer)
        except Exception as e:
            messagebox.showerror("Erreur", f"Impossible d'ouvrir la configuration avancée: {e}")

    def open_config_window(self):
        # Documentation string removed
        # Créer une nouvelle fenêtre
        config_window = tk.Toplevel(self)
        config_window.title("Paramètres de configuration")
        config_window.geometry("600x400")
        config_window.transient(self)  # Rendre la fenêtre modale
        config_window.grab_set()

        # Centrer la fenêtre
        config_window.update_idletasks()
        width = config_window.winfo_width()
        height = config_window.winfo_height()
        x = (config_window.winfo_screenwidth() // 2) - (width // 2)
        y = (config_window.winfo_screenheight() // 2) - (height // 2)
        config_window.geometry('{}x{}+{}+{}'.format(width, height, x, y))

        # Créer un frame principal
        main_frame = tk.Frame(config_window, padx=20, pady=20)
        main_frame.pack(fill=tk.BOTH, expand=True)

        # Paramètres du jeu
        game_frame = tk.LabelFrame(main_frame, text="Paramètres du jeu", padx=10, pady=10)
        game_frame.pack(fill=tk.X, pady=10)

        # Nombre max
        max_frame = tk.Frame(game_frame)
        max_frame.pack(fill=tk.X, pady=5)
        tk.Label(max_frame, text="Nombre maximum:", width=20, anchor='w').pack(side=tk.LEFT, padx=5)
        max_spinbox = tk.Spinbox(max_frame, from_=1, to=100, textvariable=self.max_number_var, width=10)
        max_spinbox.pack(side=tk.LEFT, padx=5)

        # Numéros par tirage
        nums_frame = tk.Frame(game_frame)
        nums_frame.pack(fill=tk.X, pady=5)
        tk.Label(nums_frame, text="Numéros par tirage:", width=20, anchor='w').pack(side=tk.LEFT, padx=5)
        nums_spinbox = tk.Spinbox(nums_frame, from_=1, to=40, textvariable=self.numbers_per_draw_var, width=10)
        nums_spinbox.pack(side=tk.LEFT, padx=5)

        # Paramètres de prédiction
        pred_frame = tk.LabelFrame(main_frame, text="Paramètres de prédiction", padx=10, pady=10)
        pred_frame.pack(fill=tk.X, pady=10)

        # Nombre de prédictions
        count_frame = tk.Frame(pred_frame)
        count_frame.pack(fill=tk.X, pady=5)
        tk.Label(count_frame, text="Nombre de prédictions:", width=20, anchor='w').pack(side=tk.LEFT, padx=5)
        count_spinbox = tk.Spinbox(count_frame, from_=1, to=40, textvariable=self.prediction_count_var, width=10)
        count_spinbox.pack(side=tk.LEFT, padx=5)

        # Méthode de prédiction
        method_frame = tk.Frame(pred_frame)
        method_frame.pack(fill=tk.X, pady=5)
        tk.Label(method_frame, text="Méthode:", width=20, anchor='w').pack(side=tk.LEFT, padx=5)

        # Créer le menu déroulant avec les méthodes disponibles
        prediction_methods = ["combined", "frequency", "pattern", "random"]

        # Ajouter les méthodes avancées si disponibles
        if self.advanced_analysis_available:
            prediction_methods.extend(["ml", "patterns", "series", "advanced"])

        method_menu = tk.OptionMenu(method_frame, self.prediction_method_var, *prediction_methods)
        method_menu.config(width=15)
        method_menu.pack(side=tk.LEFT, padx=5)

        # Boutons
        button_frame = tk.Frame(main_frame)
        button_frame.pack(fill=tk.X, pady=20)

        # Bouton Appliquer
        apply_button = tk.Button(button_frame, text="Appliquer", command=lambda: self.apply_settings_from_window(config_window),
                               bg='#3498db', fg='white', font=('Helvetica', 10, 'bold'), width=15)
        apply_button.pack(side=tk.LEFT, padx=10)

        # Bouton Annuler
        cancel_button = tk.Button(button_frame, text="Annuler", command=config_window.destroy,
                                bg='#e74c3c', fg='white', font=('Helvetica', 10, 'bold'), width=15)
        cancel_button.pack(side=tk.RIGHT, padx=10)

    def open_advanced_options(self):
        # Documentation string removed
        # Créer une nouvelle fenêtre
        adv_window = tk.Toplevel(self)
        adv_window.title("Options avancées d'importation")
        adv_window.geometry("700x300")
        adv_window.transient(self)  # Rendre la fenêtre modale
        adv_window.grab_set()

        # Centrer la fenêtre
        adv_window.update_idletasks()
        width = adv_window.winfo_width()
        height = adv_window.winfo_height()
        x = (adv_window.winfo_screenwidth() // 2) - (width // 2)
        y = (adv_window.winfo_screenheight() // 2) - (height // 2)
        adv_window.geometry('{}x{}+{}+{}'.format(width, height, x, y))

        # Créer un frame principal
        main_frame = tk.Frame(adv_window, padx=20, pady=20)
        main_frame.pack(fill=tk.BOTH, expand=True)

        # Options de date
        date_frame = tk.LabelFrame(main_frame, text="Options de date et heure", padx=10, pady=10)
        date_frame.pack(fill=tk.X, pady=10)

        # Format de date français
        french_frame = tk.Frame(date_frame)
        french_frame.pack(fill=tk.X, pady=5)
        self.french_date_var = tk.BooleanVar(value=True)  # Activé par défaut
        french_check = tk.Checkbutton(french_frame, text="Format de date français (DD/MM/YYYY)", variable=self.french_date_var)
        french_check.pack(side=tk.LEFT, padx=5)

        # Colonne heure
        time_frame = tk.Frame(date_frame)
        time_frame.pack(fill=tk.X, pady=5)
        tk.Label(time_frame, text="Colonne heure:", width=15, anchor='w').pack(side=tk.LEFT, padx=5)
        self.time_column_var = tk.IntVar(value=2)  # Par défaut, colonne 2 pour le format FDJ
        time_spinbox = tk.Spinbox(time_frame, from_=-1, to=20, textvariable=self.time_column_var, width=5)
        time_spinbox.pack(side=tk.LEFT, padx=5)

        # Combiner date et heure
        combine_frame = tk.Frame(date_frame)
        combine_frame.pack(fill=tk.X, pady=5)
        self.combine_date_time_var = tk.BooleanVar(value=True)  # Activé par défaut
        combine_check = tk.Checkbutton(combine_frame, text="Combiner date et heure (détecte 'midi', 'soir', 'matin', etc.)",
                                      variable=self.combine_date_time_var)
        combine_check.pack(side=tk.LEFT, padx=5)

        # Options d'ID et de conservation
        id_frame = tk.LabelFrame(main_frame, text="Options d'ID et de conservation", padx=10, pady=10)
        id_frame.pack(fill=tk.X, pady=10)

        # Colonne ID
        id_col_frame = tk.Frame(id_frame)
        id_col_frame.pack(fill=tk.X, pady=5)
        tk.Label(id_col_frame, text="Colonne ID:", width=15, anchor='w').pack(side=tk.LEFT, padx=5)
        self.id_column_var = tk.IntVar(value=0)  # Par défaut, colonne 0 pour le format FDJ
        id_spinbox = tk.Spinbox(id_col_frame, from_=-1, to=20, textvariable=self.id_column_var, width=5)
        id_spinbox.pack(side=tk.LEFT, padx=5)
        tk.Label(id_col_frame, text="(-1 pour désactiver)").pack(side=tk.LEFT, padx=5)

        # Option pour conserver les données existantes
        clear_frame = tk.Frame(id_frame)
        clear_frame.pack(fill=tk.X, pady=5)
        self.clear_existing_var = tk.BooleanVar(value=True)
        clear_check = tk.Checkbutton(clear_frame, text="Effacer données existantes", variable=self.clear_existing_var)
        clear_check.pack(side=tk.LEFT, padx=5)

        # Boutons
        button_frame = tk.Frame(main_frame)
        button_frame.pack(fill=tk.X, pady=20)

        # Bouton OK
        ok_button = tk.Button(button_frame, text="OK", command=adv_window.destroy,
                            bg='#3498db', fg='white', font=('Helvetica', 10, 'bold'), width=15)
        ok_button.pack(side=tk.LEFT, padx=10)

        # Bouton Annuler
        cancel_button = tk.Button(button_frame, text="Annuler", command=adv_window.destroy,
                                bg='#e74c3c', fg='white', font=('Helvetica', 10, 'bold'), width=15)
        cancel_button.pack(side=tk.RIGHT, padx=10)

    def apply_settings_from_window(self, window):
        # Documentation string removed
        try:
            max_number = self.max_number_var.get()
            numbers_per_draw = self.numbers_per_draw_var.get()

            if max_number < numbers_per_draw:
                messagebox.showerror("Erreur de configuration",
                                     "Le nombre maximum doit être supérieur au nombre de numéros par tirage.")
                return

            self.data_manager.set_parameters(max_number, numbers_per_draw)
            self.analyzer.update_parameters()

            messagebox.showinfo("Configuration", "Paramètres appliqués avec succès.")
            window.destroy()  # Fermer la fenêtre après avoir appliqué les paramètres
        except Exception as e:
            messagebox.showerror("Erreur", f"Erreur lors de l'application des paramètres: {e}")

    def apply_settings(self):
        # Documentation string removed
        try:
            max_number = self.max_number_var.get()
            numbers_per_draw = self.numbers_per_draw_var.get()

            if max_number < numbers_per_draw:
                messagebox.showerror("Erreur de configuration",
                                     "Le nombre maximum doit être supérieur au nombre de numéros par tirage.")
                return

            self.data_manager.set_parameters(max_number, numbers_per_draw)
            self.analyzer.update_parameters()

            messagebox.showinfo("Configuration", "Paramètres appliqués avec succès.")
        except Exception as e:
            messagebox.showerror("Erreur", f"Erreur lors de l'application des paramètres: {e}")

    def browse_csv(self):
        # Documentation string removed
        file_path = filedialog.askopenfilename(
            title="Sélectionner un fichier CSV",
            filetypes=[("Fichiers CSV", "*.csv"), ("Tous les fichiers", "*.*")]
        )
        if file_path:
            self.csv_path_var.set(file_path)

            # Mettre en évidence les boutons d'importation pour guider l'utilisateur
            if hasattr(self, 'import_button') and hasattr(self, 'import_multiple_button'):
                # Utiliser bg au lieu de style pour les boutons tk
                self.import_button.configure(bg='#e74c3c')
                self.import_multiple_button.configure(bg='#e74c3c')

            # Mettre en évidence le bouton d'importation rapide
            if hasattr(self, 'quick_import_button'):
                # Utiliser bg au lieu de style pour les boutons tk
                self.quick_import_button.configure(bg='#e74c3c')

                # Mettre à jour le texte d'instruction
                if hasattr(self, 'import_instruction_var'):
                    self.import_instruction_var.set(f"Fichier sélectionné: {os.path.basename(file_path)} - Cliquez sur 'Importer' pour valider l'importation.")

                # Afficher un message pour guider l'utilisateur
                messagebox.showinfo("Fichier sélectionné",
                                  f"Fichier sélectionné: {os.path.basename(file_path)}\n\n"
                                  f"Cliquez sur 'Importer CSV' pour importer ce fichier ou "
                                  f"'Importation multiple' pour sélectionner d'autres fichiers.")

    def get_import_options(self):
        # Documentation string removed
        # Récupérer les options d'importation de base
        options = {
            'date_format': self.date_format_var.get(),
            'date_column': self.date_column_var.get(),
            'numbers_start_column': self.numbers_start_column_var.get(),
            'has_header': self.has_header_var.get(),
            'delimiter': self.delimiter_var.get(),
            'french_date': self.french_date_var.get(),
            'time_column': self.time_column_var.get() if self.time_column_var.get() >= 0 else None,
            'combine_date_time': self.combine_date_time_var.get(),
            'id_column': self.id_column_var.get() if self.id_column_var.get() >= 0 else None,
            'clear_existing': self.clear_existing_var.get()
        }

        # Ajuster le format de date si nécessaire
        if options['french_date'] and options['combine_date_time']:
            # Format pour date française combinée avec heure
            options['date_format'] = "%Y-%m-%d %H:%M:%S"
        elif options['french_date']:
            # Format pour date française seule
            options['date_format'] = "%Y-%m-%d"

        return options

    def import_csv(self):
        # Documentation string removed
        file_path = self.csv_path_var.get()
        if not file_path:
            messagebox.showerror("Erreur", "Veuillez sélectionner un fichier CSV.")
            return

        if not os.path.exists(file_path):
            messagebox.showerror("Erreur", f"Le fichier {file_path} n'existe pas.")
            return

        try:
            # Réinitialiser le style des boutons d'importation
            if hasattr(self, 'import_button') and hasattr(self, 'import_multiple_button'):
                # Utiliser bg au lieu de style pour les boutons tk
                self.import_button.configure(bg='#3498db')
                self.import_multiple_button.configure(bg='#3498db')

            # Réinitialiser le style du bouton d'importation rapide
            if hasattr(self, 'quick_import_button'):
                # Utiliser bg au lieu de style pour les boutons tk
                self.quick_import_button.configure(bg='#3498db')

            # Réinitialiser le texte d'instruction
            if hasattr(self, 'import_instruction_var'):
                self.import_instruction_var.set("Cliquez sur 'Parcourir' pour sélectionner un fichier, puis sur 'Importer' pour valider l'importation.")

            # Appliquer les paramètres avant l'importation
            self.apply_settings()

            # Récupérer les options d'importation
            options = self.get_import_options()

            # Créer une fenêtre de progression
            progress_window = tk.Toplevel(self)
            progress_window.title("Importation en cours")
            progress_window.geometry("400x150")
            progress_window.transient(self)  # Rendre la fenêtre modale
            progress_window.grab_set()

            # Centrer la fenêtre
            progress_window.update_idletasks()
            width = progress_window.winfo_width()
            height = progress_window.winfo_height()
            x = (progress_window.winfo_screenwidth() // 2) - (width // 2)
            y = (progress_window.winfo_screenheight() // 2) - (height // 2)
            progress_window.geometry('{}x{}+{}+{}'.format(width, height, x, y))

            # Ajouter les widgets de progression
            ttk.Label(progress_window, text="Importation des données en cours...").pack(pady=10)

            progress_var = tk.DoubleVar()
            progress_bar = ttk.Progressbar(progress_window, variable=progress_var, maximum=100)
            progress_bar.pack(fill=tk.X, padx=20, pady=10)

            status_var = tk.StringVar(value="Préparation de l'importation...")
            status_label = ttk.Label(progress_window, textvariable=status_var)
            status_label.pack(pady=5)

            # Fonction de mise à jour de la progression
            def update_progress(current, total, message):
                if total > 0:
                    progress_var.set((current / total) * 100)
                status_var.set(message)
                progress_window.update_idletasks()

            # Ajouter la fonction de callback aux options
            options['progress_callback'] = update_progress

            # Importer les données en arrière-plan
            self.after(100, lambda: self._perform_import(file_path, options, progress_window))

        except Exception as e:
            messagebox.showerror("Erreur", f"Erreur lors de l'importation: {e}")

    def _perform_import(self, file_path, options, progress_window):
        # Documentation string removed
        try:
            # Vérifier que le fichier existe
            if not os.path.exists(file_path):
                progress_window.destroy()
                messagebox.showerror("Erreur", f"Le fichier {file_path} n'existe pas.")
                return

            # Vérifier que le fichier est accessible en lecture
            try:
                with open(file_path, 'r', encoding='utf-8') as f:
                    # Lire les premières lignes pour vérifier le format
                    header = f.readline()
                    if not header:
                        progress_window.destroy()
                        messagebox.showerror("Erreur", f"Le fichier {file_path} est vide.")
                        return
            except (PermissionError, IOError) as e:
                progress_window.destroy()
                messagebox.showerror("Erreur d'accès", f"Impossible d'accéder au fichier {file_path}: {e}")
                return
            except UnicodeDecodeError:
                # Essayer avec une autre encodage
                try:
                    with open(file_path, 'r', encoding='latin-1') as f:
                        header = f.readline()
                        if not header:
                            progress_window.destroy()
                            messagebox.showerror("Erreur", f"Le fichier {file_path} est vide.")
                            return
                        # Ajouter l'encodage latin-1 aux options
                        options['encoding'] = 'latin-1'
                except Exception as e:
                    progress_window.destroy()
                    messagebox.showerror("Erreur d'encodage", f"Impossible de lire le fichier {file_path}: {e}")
                    return

            # Acquérir le verrou pour éviter les conflits
            with self.thread_lock:
                # Importer les données
                result = self.data_manager.load_csv(
                    file_path,
                    **options
                )

            # Fermer la fenêtre de progression
            progress_window.destroy()

            if isinstance(result, int) and result >= 0:
                draws_count = self.data_manager.get_draws_count()
                if result > 0:
                    messagebox.showinfo("Importation réussie", f"{draws_count} tirages importés avec succès.")

                    # Sauvegarder automatiquement après une importation réussie
                    try:
                        self.save_database()
                    except Exception as e:
                        print(f"Erreur lors de la sauvegarde automatique: {e}")
                else:
                    # Si aucun tirage n'a été importé mais que l'importation s'est bien déroulée
                    messagebox.showinfo("Importation terminée", "Aucun nouveau tirage n'a été importé. Tous les tirages étaient déjà présents dans la base de données.")
            else:
                messagebox.showerror("Erreur", "Échec de l'importation du fichier CSV.")
        except Exception as e:
            # Journaliser l'erreur pour le débogage
            print(f"Erreur lors de l'importation: {e}")

            # Fermer la fenêtre de progression si elle existe encore
            try:
                progress_window.destroy()
            except:
                pass

            messagebox.showerror("Erreur", f"Erreur lors de l'importation: {e}")

    def update_stats_safely(self):
        # Documentation string removed
        try:
            # Afficher un message de progression
            self.config(cursor="wait")
            self.stats_text.delete(1.0, tk.END)
            self.stats_text.insert(tk.END, "Mise à jour des statistiques en cours...\nCela peut prendre un moment avec de grands jeux de données.")
            self.update()

            # Mettre à jour les statistiques
            self.update_stats()

            # Rétablir le curseur
            self.config(cursor="")
        except Exception as e:
            self.config(cursor="")
            print(f"Erreur lors de la mise à jour des statistiques: {e}")
            # Ne pas afficher de message d'erreur pour éviter de bloquer l'interface

    def import_multiple_csv(self):
        # Documentation string removed
        # Réinitialiser le style des boutons d'importation
        if hasattr(self, 'import_button') and hasattr(self, 'import_multiple_button'):
            self.import_button.configure(style='Action.TButton')
            self.import_multiple_button.configure(style='Action.TButton')

        # Réinitialiser le style du bouton d'importation rapide
        if hasattr(self, 'quick_import_button'):
            self.quick_import_button.configure(style='Action.TButton')

        # Réinitialiser le texte d'instruction
        if hasattr(self, 'import_instruction_var'):
            self.import_instruction_var.set("Cliquez sur 'Parcourir' pour sélectionner un fichier, puis sur 'Importer' pour valider l'importation.")

        file_paths = filedialog.askopenfilenames(
            title="Sélectionner plusieurs fichiers CSV",
            filetypes=[("Fichiers CSV", "*.csv"), ("Tous les fichiers", "*.*")]
        )

        if not file_paths:
            return

        try:
            # Appliquer les paramètres avant l'importation
            self.apply_settings()

            # Récupérer les options d'importation
            options = self.get_import_options()

            # Créer une fenêtre de progression
            progress_window = tk.Toplevel(self)
            progress_window.title("Importation multiple en cours")
            progress_window.geometry("500x200")
            progress_window.transient(self)  # Rendre la fenêtre modale
            progress_window.grab_set()

            # Centrer la fenêtre
            progress_window.update_idletasks()
            width = progress_window.winfo_width()
            height = progress_window.winfo_height()
            x = (progress_window.winfo_screenwidth() // 2) - (width // 2)
            y = (progress_window.winfo_screenheight() // 2) - (height // 2)
            progress_window.geometry('{}x{}+{}+{}'.format(width, height, x, y))

            # Ajouter les widgets de progression
            ttk.Label(progress_window, text="Importation des fichiers en cours...").pack(pady=10)

            # Progression globale
            ttk.Label(progress_window, text="Progression globale:").pack(anchor=tk.W, padx=20)
            global_progress_var = tk.DoubleVar()
            global_progress_bar = ttk.Progressbar(progress_window, variable=global_progress_var, maximum=100)
            global_progress_bar.pack(fill=tk.X, padx=20, pady=5)

            # Progression du fichier actuel
            ttk.Label(progress_window, text="Fichier actuel:").pack(anchor=tk.W, padx=20)
            file_progress_var = tk.DoubleVar()
            file_progress_bar = ttk.Progressbar(progress_window, variable=file_progress_var, maximum=100)
            file_progress_bar.pack(fill=tk.X, padx=20, pady=5)

            status_var = tk.StringVar(value="Préparation de l'importation...")
            status_label = ttk.Label(progress_window, textvariable=status_var)
            status_label.pack(pady=5)

            # Fonction de mise à jour de la progression du fichier actuel
            def update_file_progress(current, total, message):
                if total > 0:
                    file_progress_var.set((current / total) * 100)
                status_var.set(message)
                progress_window.update_idletasks()

            # Lancer l'importation en arrière-plan
            self.after(100, lambda: self._perform_multiple_import(file_paths, options, progress_window,
                                                                     global_progress_var, update_file_progress))

        except Exception as e:
            messagebox.showerror("Erreur", f"Erreur lors de l'importation multiple: {e}")

    def _perform_multiple_import(self, file_paths, options, progress_window, global_progress_var, update_file_progress):
        """Effectue l'importation de plusieurs fichiers CSV

        Args:
            file_paths (list): Liste des chemins des fichiers à importer
            options (dict): Options d'importation
            progress_window (tk.Toplevel): Fenêtre de progression
            global_progress_var (tk.DoubleVar): Variable pour la barre de progression globale
            update_file_progress (function): Fonction de mise à jour de la progression du fichier actuel
        """
        try:
            # Pour le premier fichier, utiliser clear_existing tel que défini
            # Pour les fichiers suivants, toujours conserver les données existantes
            total_imported = 0
            total_files = len(file_paths)
            failed_files = []
            skipped_files = []

            for i, file_path in enumerate(file_paths):
                try:
                    # Mettre à jour la progression globale
                    global_progress_var.set((i / total_files) * 100)
                    progress_window.update_idletasks()

                    # Mettre à jour le statut
                    update_file_progress(0, 1, f"Importation du fichier {i+1}/{total_files}: {os.path.basename(file_path)}")

                    # Vérifier que le fichier existe
                    if not os.path.exists(file_path):
                        failed_files.append((os.path.basename(file_path), "Le fichier n'existe pas"))
                        continue

                    # Vérifier l'encodage du fichier
                    try:
                        with open(file_path, 'r', encoding='utf-8') as f:
                            # Lire quelques lignes pour vérifier l'encodage
                            for _ in range(5):
                                f.readline()
                    except UnicodeDecodeError:
                        # Essayer avec latin-1
                        try:
                            with open(file_path, 'r', encoding='latin-1') as f:
                                # Lire quelques lignes pour vérifier l'encodage
                                for _ in range(5):
                                    f.readline()
                            # Si ça fonctionne, utiliser latin-1
                            options['encoding'] = 'latin-1'
                        except Exception as e:
                            failed_files.append((os.path.basename(file_path), f"Erreur d'encodage: {e}"))
                            continue

                    if i > 0:
                        options['clear_existing'] = False

                    # Ajouter la fonction de callback aux options
                    options['progress_callback'] = update_file_progress

                    # Importer les données
                    result = self.data_manager.load_csv(
                        file_path,
                        **options
                    )

                    if isinstance(result, int) and result >= 0:
                        if result > 0:
                            total_imported += 1
                        else:
                            skipped_files.append(os.path.basename(file_path))
                    else:
                        failed_files.append((os.path.basename(file_path), "Erreur lors de l'importation"))
                except Exception as e:
                    print(f"Erreur lors de l'importation du fichier {file_path}: {e}")
                    failed_files.append((os.path.basename(file_path), str(e)))

            # Mettre à jour la progression finale
            global_progress_var.set(100)
            update_file_progress(1, 1, "Importation terminée")

            # Fermer la fenêtre de progression
            progress_window.destroy()

            # Préparer le message de résultat
            if total_imported > 0 or len(skipped_files) > 0:
                draws_count = self.data_manager.get_draws_count()
                message = f"{total_imported} fichiers importés sur {total_files}.\n"
                message += f"Total de {draws_count} tirages dans la base de données.\n"

                if len(skipped_files) > 0:
                    message += f"\n{len(skipped_files)} fichiers sans nouveaux tirages:\n"
                    for file in skipped_files[:5]:  # Limiter à 5 fichiers pour éviter un message trop long
                        message += f"- {file}\n"
                    if len(skipped_files) > 5:
                        message += f"- ... et {len(skipped_files) - 5} autres fichiers\n"

                if len(failed_files) > 0:
                    message += f"\n{len(failed_files)} fichiers n'ont pas pu être importés:\n"
                    for file, error in failed_files[:5]:  # Limiter à 5 fichiers pour éviter un message trop long
                        message += f"- {file}: {error}\n"
                    if len(failed_files) > 5:
                        message += f"- ... et {len(failed_files) - 5} autres fichiers\n"

                messagebox.showinfo("Importation terminée", message)

                # Sauvegarder automatiquement après une importation réussie
                if total_imported > 0:
                    try:
                        self.save_database()
                    except Exception as e:
                        print(f"Erreur lors de la sauvegarde automatique: {e}")
            else:
                if len(failed_files) > 0:
                    message = "Aucun fichier n'a pu être importé.\n\nErreurs rencontrées:\n"
                    for file, error in failed_files[:10]:  # Limiter à 10 fichiers pour éviter un message trop long
                        message += f"- {file}: {error}\n"
                    if len(failed_files) > 10:
                        message += f"- ... et {len(failed_files) - 10} autres fichiers\n"
                    messagebox.showerror("Erreur d'importation", message)
                else:
                    messagebox.showerror("Erreur", "Échec de l'importation des fichiers CSV.")
        except Exception as e:
            # Journaliser l'erreur pour le débogage
            print(f"Erreur lors de l'importation multiple: {e}")

            # Fermer la fenêtre de progression si elle existe encore
            try:
                progress_window.destroy()
            except:
                pass

            messagebox.showerror("Erreur", f"Erreur lors de l'importation multiple: {e}")

    def generate_prediction(self):
        # Documentation string removed
        try:
            # Vérifier s'il y a des données
            if not self.data_manager.draws:
                messagebox.showwarning("Avertissement", "Aucune donnée disponible. Veuillez importer un fichier CSV.")
                return

            # Récupérer les paramètres de prédiction
            num_predictions = self.prediction_count_var.get()
            method = self.prediction_method_var.get()

            # Vérifier si la méthode avancée est disponible
            if method in ["ml", "patterns", "series", "advanced"] and not self.advanced_analysis_available:
                messagebox.showwarning("Méthode non disponible",
                                     "Les méthodes d'analyse avancée ne sont pas disponibles.\n"
                                     "Veuillez installer les bibliothèques nécessaires (scikit-learn, pandas, etc.)")
                return

            # Afficher un message de progression
            self.config(cursor="wait")
            self.prediction_text.delete(1.0, tk.END)
            self.prediction_text.insert(tk.END, "Génération de la prédiction en cours...\nCela peut prendre un moment avec de grands jeux de données.")
            self.update()

            # Générer la prédiction dans un thread séparé
            threading.Thread(target=self._generate_prediction_in_thread, args=(num_predictions, method)).start()

        except Exception as e:
            self.config(cursor="")
            messagebox.showerror("Erreur", f"Erreur lors de la génération de la prédiction: {e}")

    def _generate_prediction_in_thread(self, num_predictions, method):
        """
        Génère une prédiction dans un thread séparé

        Args:
            num_predictions (int): Nombre de numéros à prédire
            method (str): Méthode de prédiction
        """
        try:
            # Générer la prédiction
            print(f"Début de la génération de prédiction avec la méthode {method} pour {num_predictions} numéros")

            # Vérifier si la méthode est 'auto'
            if method == "auto":
                # Utiliser la méthode combinée par défaut
                method = "combined"
                print("Méthode 'auto' détectée, utilisation de la méthode 'combined'")

            # Vérifier si la méthode est 'reinforcement_learning'
            elif method == "reinforcement_learning":
                # Vérifier si l'agent RL est disponible
                if hasattr(self, 'feedback_manager') and self.feedback_manager and hasattr(self.feedback_manager, 'rl_agent') and self.feedback_manager.rl_agent:
                    print("Utilisation de l'apprentissage par renforcement pour la prédiction")
                else:
                    # Fallback à la méthode combinée
                    method = "combined"
                    print("Agent RL non disponible, utilisation de la méthode 'combined'")

            # Générer la prédiction avec gestion d'erreur améliorée
            try:
                predicted_numbers, confidence = self.analyzer.predict_next_draw(num_predictions, method)
                print(f"Prédiction générée avec succès: {predicted_numbers}, confiance: {confidence}%")

                # Vérifier que les résultats sont valides
                if not predicted_numbers or not isinstance(predicted_numbers, list):
                    raise ValueError(f"La prédiction n'a pas retourné de numéros valides: {predicted_numbers}")

                # Mettre à jour l'interface dans le thread principal
                self.after(0, lambda: self._update_prediction_ui(predicted_numbers, confidence, method))
            except Exception as e:
                print(f"Erreur lors de l'appel à predict_next_draw: {e}")
                # Essayer avec la méthode de fréquence comme fallback
                try:
                    print("Tentative de repli sur la méthode 'frequency'")
                    predicted_numbers, confidence = self.analyzer._predict_by_frequency(num_predictions)
                    print(f"Prédiction de secours générée: {predicted_numbers}, confiance: {confidence}%")
                    self.after(0, lambda: self._update_prediction_ui(predicted_numbers, confidence, "frequency (fallback)"))
                except Exception as e2:
                    print(f"Erreur lors de la génération de prédiction de secours: {e2}")
                    self.after(0, lambda: self._show_prediction_error(f"Erreur principale: {e}\n\nErreur de secours: {e2}"))
        except Exception as e:
            print(f"Erreur générale lors de la génération de la prédiction: {e}")
            self.after(0, lambda: self._show_prediction_error(str(e)))

    def _show_prediction_error(self, error_message):
        """Affiche un message d'erreur formaté dans la zone de prédiction"""
        self.config(cursor="")
        self.prediction_text.delete(1.0, tk.END)

        # Configurer les tags pour le formatage du texte d'erreur
        self.prediction_text.tag_configure('error_title', font=('Helvetica', self.font_size+4, 'bold'), foreground='#e74c3c')
        self.prediction_text.tag_configure('error_message', font=('Helvetica', self.font_size), foreground='#e74c3c')

        # Insérer le message d'erreur formaté
        self.prediction_text.insert(tk.END, "\n\nERREUR DE PRÉDICTION\n\n", 'error_title')
        self.prediction_text.insert(tk.END, f"Détails de l'erreur:\n{error_message}\n\n", 'error_message')
        self.prediction_text.insert(tk.END, "Suggestions:\n", 'error_title')
        self.prediction_text.insert(tk.END, "- Vérifiez que vous avez importé suffisamment de données\n", 'error_message')
        self.prediction_text.insert(tk.END, "- Essayez une méthode de prédiction différente\n", 'error_message')
        self.prediction_text.insert(tk.END, "- Assurez-vous que les données importées sont valides\n", 'error_message')

    def _update_prediction_ui(self, predicted_numbers, confidence, method):
        """Met à jour l'interface utilisateur avec les résultats de prédiction formatés de manière améliorée"""
        try:
            # Stocker la prédiction actuelle pour validation ultérieure
            self.current_prediction = {
                'numbers': predicted_numbers,
                'confidence': confidence,
                'method': method,
                'timestamp': datetime.now()
            }

            # Effacer le contenu précédent
            self.prediction_text.delete(1.0, tk.END)

            # Configurer les tags pour le formatage du texte avec des couleurs plus vives
            self.prediction_text.tag_configure('title', font=('Helvetica', self.font_size+8, 'bold'), justify='center', foreground='#1e3799')
            self.prediction_text.tag_configure('subtitle', font=('Helvetica', self.font_size+4, 'bold'), foreground='#3498db')
            self.prediction_text.tag_configure('info', font=('Helvetica', self.font_size+2), foreground='#2c3e50')
            self.prediction_text.tag_configure('numbers', font=('Courier New', self.font_size+6, 'bold'), foreground='#e74c3c')
            self.prediction_text.tag_configure('number_box', background='#f1c40f', foreground='#2c3e50', font=('Courier New', self.font_size+6, 'bold'))
            self.prediction_text.tag_configure('footer', font=('Helvetica', self.font_size), foreground='#7f8c8d', justify='right')

            # Traduire le nom de la méthode
            method_names = {
                'frequency': 'Fréquence',
                'pattern': 'Motifs',
                'random': 'Aléatoire',
                'combined': 'Combinée',
                'ml': 'Apprentissage automatique',
                'patterns': 'Analyse des motifs',
                'series': 'Analyse des séries',
                'advanced': 'Analyse avancée combinée',
                'auto': 'Sélection automatique'
            }

            method_name = method_names.get(method, method.capitalize())

            # Insérer le titre centré
            self.prediction_text.insert(tk.END, "\n", 'title')
            self.prediction_text.insert(tk.END, "PRÉDICTION POUR LE PROCHAIN TIRAGE\n\n", 'title')

            # Insérer les informations sur la méthode et la confiance
            self.prediction_text.insert(tk.END, "Méthode: ", 'subtitle')
            self.prediction_text.insert(tk.END, f"{method_name}\n", 'info')

            self.prediction_text.insert(tk.END, "Confiance: ", 'subtitle')
            self.prediction_text.insert(tk.END, f"{confidence:.2f}%\n\n", 'info')

            # Insérer le sous-titre pour les numéros prédits
            self.prediction_text.insert(tk.END, "NUMÉROS PRÉDITS:\n\n", 'subtitle')

            # Afficher les numéros en grille avec un formatage amélioré
            sorted_numbers = sorted(predicted_numbers)

            # Créer une grille de numéros plus lisible avec des boîtes colorées
            # Insérer d'abord un espace pour le centrage
            self.prediction_text.insert(tk.END, "\n", 'numbers')

            # Créer une grille de 5 colonnes
            row_count = (len(sorted_numbers) + 4) // 5  # Arrondir au supérieur

            for row in range(row_count):
                # Ajouter un espace au début de chaque ligne pour le centrage
                self.prediction_text.insert(tk.END, "   ", 'numbers')

                # Ajouter les numéros de cette ligne
                for col in range(5):
                    idx = row * 5 + col
                    if idx < len(sorted_numbers):
                        # Insérer le numéro dans une boîte colorée
                        num = sorted_numbers[idx]
                        self.prediction_text.insert(tk.END, f" {num:2d} ", 'number_box')
                        # Ajouter un espace entre les boîtes
                        self.prediction_text.insert(tk.END, "  ", 'numbers')

                # Ajouter un saut de ligne à la fin de chaque ligne
                self.prediction_text.insert(tk.END, "\n\n", 'numbers')

            # Ajouter des informations supplémentaires dans le pied de page
            draws_count = self.data_manager.get_draws_count()
            timestamp = datetime.now().strftime('%Y-%m-%d %H:%M:%S')

            footer_text = f"\nBasé sur l'analyse de {draws_count} tirages.\n"
            footer_text += f"Généré le {timestamp}\n"

            self.prediction_text.insert(tk.END, footer_text, 'footer')

            # Mettre à jour les informations de prédiction pour l'exportation
            self.current_prediction['draws_count'] = draws_count

            # Rétablir le curseur
            self.config(cursor="")

        except Exception as e:
            self.config(cursor="")
            messagebox.showerror("Erreur", "Erreur lors de la mise à jour de l'interface: {}".format(e))

    def export_prediction(self):
        # Documentation string removed
        # Vérifier si une prédiction a été générée
        if not hasattr(self, 'current_prediction'):
            messagebox.showwarning("Avertissement", "Aucune prédiction à exporter. Veuillez d'abord générer une prédiction.")
            return

        # Demander le chemin du fichier d'exportation
        file_path = filedialog.asksaveasfilename(
            title="Exporter la prédiction",
            defaultextension=".txt",
            filetypes=[("Fichiers texte", "*.txt"), ("Fichiers CSV", "*.csv"), ("Tous les fichiers", "*.*")]
        )

        if not file_path:
            return

        try:
            # Déterminer le format d'exportation en fonction de l'extension
            _, ext = os.path.splitext(file_path)

            if ext.lower() == '.csv':
                # Exporter au format CSV
                self.export_prediction_csv(file_path)
            else:
                # Exporter au format texte par défaut
                self.export_prediction_text(file_path)

            messagebox.showinfo("Exportation réussie", f"Prédiction exportée avec succès dans {file_path}")
        except Exception as e:
            messagebox.showerror("Erreur", f"Erreur lors de l'exportation: {e}")

    def show_feedback_dialog(self):
        """Affiche la fenêtre de saisie des résultats réels"""
        # Vérifier que le gestionnaire de feedback est disponible
        if not self.feedback_manager:
            try:
                from keno_feedback import KenoFeedback
                self.feedback_manager = KenoFeedback(self.data_manager, self.analyzer)
                print("Gestionnaire de feedback initialisé avec succès")
            except ImportError as e:
                messagebox.showwarning("Avertissement", f"Le module de feedback n'est pas disponible: {e}")
                return
            except Exception as e:
                messagebox.showerror("Erreur", f"Erreur lors de l'initialisation du gestionnaire de feedback: {e}")
                return

        # Récupérer les données de prédiction actuelles
        prediction_data = {}

        # Récupérer la méthode de prédiction
        prediction_data['method'] = self.prediction_method_var.get()

        # Récupérer les numéros prédits
        if hasattr(self, 'current_prediction') and self.current_prediction:
            prediction_data['numbers'] = self.current_prediction['numbers']
            # Stocker la prédiction actuelle pour référence
            self.last_prediction = self.current_prediction['numbers']

        # Importer la classe de dialogue
        try:
            from keno_feedback_dialog import KenoFeedbackDialog
            # Créer la fenêtre de saisie
            feedback_dialog = KenoFeedbackDialog(self, self.feedback_manager, prediction_data)
        except Exception as e:
            messagebox.showerror("Erreur", f"Impossible d'ouvrir la fenêtre de saisie des résultats: {e}")

    def export_prediction_text(self, file_path):
        # Documentation string removed
        with open(file_path, 'w', encoding='utf-8') as f:
            # En-tête
            f.write(f"PRÉDICTION KENO\n")
            f.write(f"=============\n\n")

            # Informations sur la prédiction
            f.write(f"Date de génération: {self.current_prediction['timestamp'].strftime('%Y-%m-%d %H:%M:%S')}\n")
            f.write(f"Méthode: {self.current_prediction['method']}\n")
            f.write(f"Confiance: {self.current_prediction['confidence']:.2f}%\n")
            f.write(f"Basé sur l'analyse de {self.current_prediction['draws_count']} tirages\n\n")

            # Numéros prédits
            f.write(f"Numéros prédits ({len(self.current_prediction['numbers'])}):\n")

            # Afficher les numéros en grille
            numbers = sorted(self.current_prediction['numbers'])
            for i, num in enumerate(numbers):
                f.write(f"{num:2d} ")
                if (i + 1) % 5 == 0:
                    f.write("\n")

            # Ajouter un saut de ligne si nécessaire
            if len(numbers) % 5 != 0:
                f.write("\n")

    def export_prediction_csv(self, file_path):
        # Documentation string removed
        with open(file_path, 'w', newline='', encoding='utf-8') as f:
            writer = csv.writer(f)

            # En-tête
            writer.writerow(['Date', 'Méthode', 'Confiance', 'Nombre de tirages analysés', 'Nombre de prédictions'] +
                           [f'Numéro {i+1}' for i in range(len(self.current_prediction['numbers']))])

            # Données
            row = [
                self.current_prediction['timestamp'].strftime('%Y-%m-%d %H:%M:%S'),
                self.current_prediction['method'],
                f"{self.current_prediction['confidence']:.2f}%",
                self.current_prediction['draws_count'],
                len(self.current_prediction['numbers'])
            ]

            # Ajouter les numéros prédits (triés)
            row.extend(sorted(self.current_prediction['numbers']))

            writer.writerow(row)

    def update_stats_safely(self):
        """Met à jour les statistiques de base sans déclencher l'auto-amélioration"""
        try:
            # Vérifier si le widget de texte existe
            if not hasattr(self, 'stats_text'):
                print("Widget de statistiques non disponible")
                return

            # Changer le curseur pour indiquer le chargement
            self.config(cursor="wait")
            self.update()

            if not self.data_manager.draws:
                self.stats_text.delete(1.0, tk.END)
                self.stats_text.insert(tk.END, "Aucune donnée disponible.")
                # Mettre à jour le graphique avec un message vide
                self.update_graph({})
                self.config(cursor="")
                return

            # Mettre à jour le texte des statistiques avec des informations de base
            self.stats_text.delete(1.0, tk.END)
            self.stats_text.insert(tk.END, "STATISTIQUES DE BASE\n")
            self.stats_text.insert(tk.END, "Nombre total de tirages: {}\n\n".format(self.data_manager.get_draws_count()))

            # Afficher les numéros chauds (hot numbers) sans utiliser l'analyseur avancé
            self.stats_text.insert(tk.END, "Numéros les plus fréquents (hot numbers):\n")
            hot_numbers = self.data_manager.get_hot_numbers(10)
            if hot_numbers:
                for num, freq in hot_numbers:
                    self.stats_text.insert(tk.END, "  {:2d}: {:.2f}%\n".format(num, freq))
            else:
                self.stats_text.insert(tk.END, "  Aucune donnée disponible\n")

            # Afficher les numéros froids (cold numbers) sans utiliser l'analyseur avancé
            self.stats_text.insert(tk.END, "\nNuméros les moins fréquents (cold numbers):\n")
            cold_numbers = self.data_manager.get_cold_numbers(10)
            if cold_numbers:
                for num, freq in cold_numbers:
                    self.stats_text.insert(tk.END, "  {:2d}: {:.2f}%\n".format(num, freq))
            else:
                self.stats_text.insert(tk.END, "  Aucune donnée disponible\n")

            # Mettre à jour le graphique avec les fréquences de base
            frequencies = self.data_manager.get_number_frequency()
            self.update_graph({"hot_numbers": hot_numbers})

            # Rétablir le curseur
            self.config(cursor="")

            # Afficher un message indiquant comment obtenir des statistiques complètes
            self.stats_text.insert(tk.END, "\n\nPour obtenir des statistiques complètes et des prédictions,\n")
            self.stats_text.insert(tk.END, "utilisez l'onglet Prédiction et cliquez sur 'CALCULER LA PRÉDICTION'.\n")

        except Exception as e:
            self.config(cursor="")
            messagebox.showerror("Erreur", f"Erreur lors de la mise à jour des statistiques: {e}")

    def update_stats(self):
        # Documentation string removed
        try:
            # Vérifier si le widget de texte existe
            if not hasattr(self, 'stats_text'):
                print("Widget de statistiques non disponible")
                return

            # Vérifier s'il y a des données
            if not self.data_manager.draws:
                self.stats_text.delete(1.0, tk.END)
                self.stats_text.insert(tk.END, "Aucune donnée disponible.")
                # Mettre à jour le graphique avec un message vide
                self.update_graph({})
                return

            # Lancer le calcul des statistiques dans un thread séparé
            threading.Thread(target=self._calculate_stats_in_thread).start()

        except Exception as e:
            self.config(cursor="")
            messagebox.showerror("Erreur", f"Erreur lors de la mise à jour des statistiques: {e}")

    def _calculate_stats_in_thread(self):
        # Documentation string removed
        try:
            # Récupérer les statistiques
            num_predictions = self.prediction_count_var.get()
            stats = self.analyzer.get_prediction_stats(num_predictions)

            # Mettre à jour l'interface dans le thread principal
            self.after(0, lambda: self._update_stats_ui(stats))
        except Exception as e:
            print(f"Erreur lors du calcul des statistiques: {e}")
            self.after(0, lambda: self._show_stats_error(str(e)))

    def _show_stats_error(self, error_message):
        # Documentation string removed
        self.config(cursor="")
        self.stats_text.delete(1.0, tk.END)
        self.stats_text.insert(tk.END, f"Erreur lors de la récupération des statistiques: {error_message}")
        self.update_graph({})

    def _update_stats_ui(self, stats):
        # Documentation string removed
        try:
            # Mettre à jour le texte des statistiques
            self.stats_text.delete(1.0, tk.END)

            self.stats_text.insert(tk.END, "STATISTIQUES\n")
            self.stats_text.insert(tk.END, "Nombre total de tirages: {}\n\n".format(stats.get('total_draws', 0)))

            # Afficher les numéros chauds (hot numbers)
            self.stats_text.insert(tk.END, "Numéros les plus fréquents (hot numbers):\n")
            if 'hot_numbers' in stats and stats['hot_numbers']:
                for num, freq in stats['hot_numbers']:
                    self.stats_text.insert(tk.END, "  {:2d}: {:.2f}%\n".format(num, freq))
            else:
                self.stats_text.insert(tk.END, "  Aucune donnée disponible\n")

            self.stats_text.insert(tk.END, "\nNuméros les moins fréquents (cold numbers):\n")
            if 'cold_numbers' in stats and stats['cold_numbers']:
                for num, freq in stats['cold_numbers']:
                    self.stats_text.insert(tk.END, "  {:2d}: {:.2f}%\n".format(num, freq))
            else:
                self.stats_text.insert(tk.END, "  Aucune donnée disponible\n")

            self.stats_text.insert(tk.END, "\nNuméros attendus (due numbers):\n")
            if 'due_numbers' in stats and stats['due_numbers']:
                for num, draws_since in stats['due_numbers']:
                    self.stats_text.insert(tk.END, "  {:2d}: {} tirages\n".format(num, draws_since))
            else:
                self.stats_text.insert(tk.END, "  Aucune donnée disponible\n")

            # Mettre à jour le graphique
            self.update_graph(stats)

            # Rétablir le curseur
            self.config(cursor="")

        except Exception as e:
            self.config(cursor="")
            messagebox.showerror("Erreur", "Erreur lors de la mise à jour de l'interface: {}".format(e))

    def update_graph(self, stats):
        # Documentation string removed
        # Vérifier si matplotlib est disponible
        if not matplotlib_available:
            return

        try:
            # Vérifier si le graphique est disponible
            if not hasattr(self, 'ax') or not hasattr(self, 'canvas'):
                print("Graphique non disponible")
                return

            # Effacer le graphique précédent
            self.ax.clear()

            # Vérifier s'il y a des données
            if not self.data_manager.draws:
                self.ax.text(0.5, 0.5, "Aucune donnée disponible",
                           horizontalalignment='center', verticalalignment='center',
                           transform=self.ax.transAxes)
                self.canvas.draw()
                return

            # Récupérer les fréquences de tous les numéros
            frequencies = self.data_manager.get_number_frequency()

            # Vérifier si frequencies est vide
            if not frequencies:
                self.ax.text(0.5, 0.5, "Aucune fréquence disponible",
                           horizontalalignment='center', verticalalignment='center',
                           transform=self.ax.transAxes)
                self.canvas.draw()
                return

            # Créer les données pour le graphique
            numbers = list(range(1, self.data_manager.max_number + 1))
            freqs = [frequencies.get(num, 0) for num in numbers]  # Utiliser get() avec valeur par défaut

            # Créer le graphique à barres
            bars = self.ax.bar(numbers, freqs, color='skyblue')

            # Mettre en évidence les numéros chauds si disponibles
            if 'hot_numbers' in stats and stats['hot_numbers']:
                hot_numbers = [num for num, _ in stats['hot_numbers']]
                for i, num in enumerate(numbers):
                    if i < len(bars) and num in hot_numbers:
                        bars[i].set_color('red')

            # Ajouter les étiquettes et le titre
            self.ax.set_xlabel('Numéro')
            self.ax.set_ylabel('Fréquence (%)')
            self.ax.set_title('Fréquence d\'apparition des numéros')

            # Limiter le nombre d'étiquettes sur l'axe x pour éviter l'encombrement
            step = max(1, self.data_manager.max_number // 10)  # Ajuster dynamiquement le pas

            # Utiliser une méthode compatible avec toutes les versions de matplotlib
            try:
                # Pour les versions récentes de matplotlib
                self.ax.set_xticks(range(0, self.data_manager.max_number + 1, step))
            except Exception:
                # Pour les versions plus anciennes ou en cas d'erreur
                # Vérifier si numpy est disponible
                if numpy_available:
                    self.ax.set_xticks(numpy.arange(0, self.data_manager.max_number + 1, step))
                else:
                    # Si numpy n'est pas disponible, utiliser une liste simple
                    self.ax.set_xticks([i for i in range(0, self.data_manager.max_number + 1, step)])

            # Rafraîchir le canevas
            self.canvas.draw()

        except Exception as e:
            print("Erreur lors de la mise à jour du graphique: {}".format(e))
            # Ne pas propager l'erreur pour éviter le plantage

    def clear_data(self):
        # Documentation string removed
        if messagebox.askyesno("Confirmation", "Êtes-vous sûr de vouloir effacer toutes les données?"):
            # Effacer les données
            self.data_manager.clear_data()

            # Nettoyer l'interface
            self.prediction_text.delete(1.0, tk.END)
            self.stats_text.delete(1.0, tk.END)

            # Nettoyer les graphiques
            if hasattr(self, 'ax') and self.ax:
                self.ax.clear()
                if hasattr(self, 'canvas') and self.canvas:
                    self.canvas.draw()

            # Nettoyer la mémoire
            self._clean_memory()

            messagebox.showinfo("Information", "Données effacées avec succès.")

    def _clean_memory(self):
        # Documentation string removed
        try:
            # Réinitialiser les prédictions actuelles
            if hasattr(self, 'current_prediction'):
                self.current_prediction = None

            # Nettoyer les ressources matplotlib si elles existent
            if matplotlib_available and hasattr(self, 'fig') and self.fig is not None:
                try:
                    plt.close(self.fig)
                    self.fig = None
                except Exception as e:
                    print("Erreur lors de la fermeture du graphique: {}".format(e))

            # Nettoyer les ressources de l'analyseur
            if hasattr(self, 'analyzer') and self.analyzer is not None:
                try:
                    if hasattr(self.analyzer, 'clean_resources'):
                        self.analyzer.clean_resources()
                except Exception as e:
                    print("Erreur lors du nettoyage des ressources de l'analyseur: {}".format(e))

            # Forcer le garbage collector
            import gc
            gc.collect()
        except Exception as e:
            print("Erreur lors du nettoyage de la mémoire: {}".format(e))

    def save_learning_data(self):
        # Documentation string removed
        if not learning_data_module_available:
            messagebox.showerror("Erreur", "Module de sauvegarde des données d'apprentissage non disponible.")
            return

        file_path = filedialog.asksaveasfilename(
            title="Sauvegarder les données d'apprentissage",
            defaultextension=".json",
            filetypes=[("Fichiers JSON", "*.json"), ("Fichiers Pickle", "*.pkl"), ("Tous les fichiers", "*.*")]
        )

        if file_path:
            # Normaliser le chemin du fichier
            file_path = os.path.normpath(file_path)

            try:
                success = save_learning_data(self.analyzer, file_path)
                if success:
                    messagebox.showinfo("Sauvegarde", "Données d'apprentissage sauvegardées avec succès.")
                else:
                    messagebox.showerror("Erreur", "Erreur lors de la sauvegarde des données d'apprentissage.")
            except Exception as e:
                messagebox.showerror("Erreur", f"Erreur lors de la sauvegarde des données d'apprentissage: {e}")

    def load_learning_data(self):
        # Documentation string removed
        if not learning_data_module_available:
            messagebox.showerror("Erreur", "Module de chargement des données d'apprentissage non disponible.")
            return

        file_path = filedialog.askopenfilename(
            title="Charger les données d'apprentissage",
            filetypes=[("Fichiers JSON", "*.json"), ("Fichiers Pickle", "*.pkl"), ("Tous les fichiers", "*.*")]
        )

        if file_path:
            # Normaliser le chemin du fichier
            file_path = os.path.normpath(file_path)

            try:
                success = load_learning_data(self.analyzer, file_path)
                if success:
                    messagebox.showinfo("Chargement", "Données d'apprentissage chargées avec succès.")
                else:
                    messagebox.showerror("Erreur", "Erreur lors du chargement des données d'apprentissage.")
            except Exception as e:
                messagebox.showerror("Erreur", f"Erreur lors du chargement des données d'apprentissage: {e}")

    def save_config(self):
        # Documentation string removed
        try:
            config = {
                "max_number": self.max_number_var.get(),
                "numbers_per_draw": self.numbers_per_draw_var.get(),
                "prediction_count": self.prediction_count_var.get(),
                "prediction_method": self.prediction_method_var.get(),
                "csv_import": {
                    "date_format": self.date_format_var.get(),
                    "date_column": self.date_column_var.get(),
                    "numbers_start_column": self.numbers_start_column_var.get(),
                    "has_header": self.has_header_var.get(),
                    "delimiter": self.delimiter_var.get()
                }
            }

            file_path = filedialog.asksaveasfilename(
                title="Sauvegarder la configuration",
                defaultextension=".json",
                filetypes=[("Fichiers JSON", "*.json"), ("Tous les fichiers", "*.*")]
            )

            if file_path:
                # Normaliser le chemin du fichier
                file_path = os.path.normpath(file_path)
                with open(file_path, 'w') as f:
                    json.dump(config, f, indent=4)
                messagebox.showinfo("Sauvegarde", "Configuration sauvegardée avec succès dans {}.".format(file_path))
        except Exception as e:
            messagebox.showerror("Erreur", "Erreur lors de la sauvegarde de la configuration: {}".format(e))

    def load_config(self):
        # Documentation string removed
        try:
            file_path = filedialog.askopenfilename(
                title="Charger une configuration",
                filetypes=[("Fichiers JSON", "*.json"), ("Tous les fichiers", "*.*")]
            )

            if file_path:
                # Normaliser le chemin du fichier
                file_path = os.path.normpath(file_path)
                with open(file_path, 'r') as f:
                    config = json.load(f)

                # Appliquer la configuration
                self.max_number_var.set(config.get("max_number", 70))
                self.numbers_per_draw_var.set(config.get("numbers_per_draw", 20))
                self.prediction_count_var.set(config.get("prediction_count", 10))
                self.prediction_method_var.set(config.get("prediction_method", "combined"))

                # Appliquer les options d'importation CSV
                csv_import = config.get("csv_import", {})
                self.date_format_var.set(csv_import.get("date_format", "%Y-%m-%d %H:%M:%S"))
                self.date_column_var.set(csv_import.get("date_column", 0))
                self.numbers_start_column_var.set(csv_import.get("numbers_start_column", 1))
                self.has_header_var.set(csv_import.get("has_header", True))
                self.delimiter_var.set(csv_import.get("delimiter", ","))

                # Appliquer les paramètres
                self.apply_settings()

                messagebox.showinfo("Chargement", "Configuration chargée avec succès.")
        except Exception as e:
            messagebox.showerror("Erreur", "Erreur lors du chargement de la configuration: {}".format(e))

    def save_database(self):
        # Documentation string removed
        if not self.data_manager.draws:
            messagebox.showwarning("Avertissement", "Aucune donnée à sauvegarder. Veuillez d'abord importer des données.")
            return

        file_path = filedialog.asksaveasfilename(
            title="Sauvegarder la base de données",
            defaultextension=".keno",
            filetypes=[("Fichiers Keno", "*.keno"), ("Fichiers JSON", "*.json"), ("Tous les fichiers", "*.*")]
        )

        if file_path:
            # Normaliser le chemin du fichier
            file_path = os.path.normpath(file_path)

            success = self.data_manager.save_database(file_path)
            if success:
                # Récupérer la liste des fichiers sources
                source_files = self.data_manager.get_source_files()
                source_files_str = "\n- " + "\n- ".join(source_files) if source_files else "Aucun fichier source identifié"

                messagebox.showinfo("Sauvegarde", "Base de données sauvegardée avec succès.\n\n{} tirages sauvegardés.\n\nFichiers sources inclus: {}\n{}".format(
                    self.data_manager.get_draws_count(), len(source_files), source_files_str))
            else:
                messagebox.showerror("Erreur", "Erreur lors de la sauvegarde de la base de données.")

    def load_database(self):
        # Documentation string removed
        file_path = filedialog.askopenfilename(
            title="Charger une base de données",
            filetypes=[("Fichiers Keno", "*.keno"), ("Fichiers JSON", "*.json"), ("Tous les fichiers", "*.*")]
        )

        if file_path:
            # Normaliser le chemin du fichier
            file_path = os.path.normpath(file_path)

            if messagebox.askyesno("Confirmation", "Voulez-vous remplacer les données actuelles ? Cliquez sur Non pour ajouter aux données existantes."):
                # Effacer les données existantes
                self.data_manager.draws = []

            success = self.data_manager.load_database(file_path)
            if success:
                draws_count = self.data_manager.get_draws_count()
                # Récupérer la liste des fichiers sources
                source_files = self.data_manager.get_source_files()
                source_files_str = "\n- " + "\n- ".join(source_files) if source_files else "Aucun fichier source identifié"

                messagebox.showinfo("Chargement", "Base de données chargée avec succès.\n\n{} tirages disponibles.\n\nFichiers sources inclus: {}\n{}".format(
                    draws_count, len(source_files), source_files_str))

                # Mettre à jour les statistiques de base sans déclencher l'auto-amélioration
                self.update_stats_safely()
            else:
                messagebox.showerror("Erreur", "Erreur lors du chargement de la base de données.")

    def download_data(self):
        """Ouvre une fenêtre de dialogue pour télécharger des données Keno"""
        # Créer une fenêtre de dialogue pour saisir l'URL
        dialog = tk.Toplevel(self)
        dialog.title("Télécharger des données")
        dialog.geometry("600x300")
        dialog.resizable(False, False)
        dialog.transient(self)  # Rendre la fenêtre modale
        dialog.grab_set()

        # Centrer la fenêtre
        dialog.update_idletasks()
        width = dialog.winfo_width()
        height = dialog.winfo_height()
        x = (dialog.winfo_screenwidth() // 2) - (width // 2)
        y = (dialog.winfo_screenheight() // 2) - (height // 2)
        dialog.geometry('{}x{}+{}+{}'.format(width, height, x, y))

        # Ajouter les widgets
        ttk.Label(dialog, text="Choisissez une source de données:", font=("Helvetica", 12, "bold")).pack(pady=10)

        # Option pour télécharger depuis le site de la FDJ
        fdj_frame = ttk.Frame(dialog)
        fdj_frame.pack(pady=5, padx=10, fill=tk.X)

        source_var = tk.IntVar(value=1)
        ttk.Radiobutton(fdj_frame, text="Télécharger les derniers résultats depuis le site de la FDJ",
                       variable=source_var, value=1).pack(side=tk.LEFT, padx=5)

        # Option pour télécharger depuis une URL personnalisée
        url_frame = ttk.Frame(dialog)
        url_frame.pack(pady=5, padx=10, fill=tk.X)

        ttk.Radiobutton(url_frame, text="Télécharger depuis une URL personnalisée:",
                       variable=source_var, value=2).pack(side=tk.LEFT, padx=5)

        # Champ pour l'URL
        url_var = tk.StringVar(value="https://www.fdj.fr/jeux-de-tirage/keno-gagnant-a-vie/resultats")
        ttk.Entry(dialog, textvariable=url_var, width=60, font=("Helvetica", 10)).pack(pady=5, padx=20, fill=tk.X)

        # Option pour télécharger depuis l'API FDJ (nouveau)
        api_frame = ttk.Frame(dialog)
        api_frame.pack(pady=5, padx=10, fill=tk.X)

        ttk.Radiobutton(api_frame, text="Télécharger depuis l'API FDJ (fichier ZIP):",
                       variable=source_var, value=3).pack(side=tk.LEFT, padx=5)

        # Champ pour l'URL de l'API
        api_url_var = tk.StringVar(value="https://www.sto.api.fdj.fr/anonymous/service-draw-info/v3/documentations/1a2b3c4d-9876-4562-b3fc-2c963f66aft6")
        ttk.Entry(dialog, textvariable=api_url_var, width=60, font=("Helvetica", 10)).pack(pady=5, padx=20, fill=tk.X)

        # Note explicative
        ttk.Label(dialog, text="Note: L'URL par défaut est celle de l'API FDJ qui fournit les données Keno.",
                 font=("Helvetica", 8), foreground="#555555").pack(pady=0, padx=20)

        # Nombre de jours à télécharger
        days_frame = ttk.Frame(dialog)
        days_frame.pack(pady=5, padx=10, fill=tk.X)

        ttk.Label(days_frame, text="Nombre de jours à télécharger (1-60):").pack(side=tk.LEFT, padx=5)

        days_var = tk.IntVar(value=30)
        ttk.Spinbox(days_frame, from_=1, to=60, textvariable=days_var, width=5).pack(side=tk.LEFT, padx=5)

        # Boutons
        buttons_frame = ttk.Frame(dialog)
        buttons_frame.pack(pady=15, fill=tk.X)

        ttk.Button(buttons_frame, text="Annuler", command=dialog.destroy, width=15).pack(side=tk.RIGHT, padx=10)

        def download():
            source_type = source_var.get()
            url = url_var.get().strip() if source_type == 2 else api_url_var.get().strip() if source_type == 3 else ""
            num_days = days_var.get()

            if source_type in [2, 3] and not url:
                messagebox.showerror("Erreur", "Veuillez entrer une URL valide.")
                return

            dialog.destroy()  # Fermer la fenêtre de dialogue

            # Demander confirmation pour l'ajout aux données existantes
            if self.data_manager.draws and messagebox.askyesno("Confirmation", "Voulez-vous remplacer les données actuelles ? Cliquez sur Non pour ajouter aux données existantes."):
                # Effacer les données existantes
                self.data_manager.draws = []

            # Créer une fenêtre de progression
            progress_window = tk.Toplevel(self)
            progress_window.title("Téléchargement en cours")
            progress_window.geometry("400x150")
            progress_window.resizable(False, False)
            progress_window.transient(self)
            progress_window.grab_set()

            # Centrer la fenêtre
            progress_window.update_idletasks()
            width = progress_window.winfo_width()
            height = progress_window.winfo_height()
            x = (progress_window.winfo_screenwidth() // 2) - (width // 2)
            y = (progress_window.winfo_screenheight() // 2) - (height // 2)
            progress_window.geometry('{}x{}+{}+{}'.format(width, height, x, y))

            # Ajouter les widgets de progression
            progress_var = tk.DoubleVar()
            status_var = tk.StringVar(value="Initialisation...")

            ttk.Label(progress_window, textvariable=status_var).pack(pady=10)
            progress_bar = ttk.Progressbar(progress_window, variable=progress_var, maximum=100, length=350)
            progress_bar.pack(pady=10, padx=20)

            try:
                if source_type == 1:  # Site FDJ
                    # Importer le module de téléchargement automatique
                    try:
                        from auto_download import download_fdj_data_thread
                        download_fdj_data_thread(self, progress_window, progress_var, status_var, num_days)
                    except ImportError:
                        # Fallback si le module n'est pas disponible
                        progress_window.destroy()
                        messagebox.showinfo("Téléchargement en cours",
                                          "L'application va télécharger les résultats des {} derniers jours depuis le site de la FDJ.\n\n"
                                          "Cette opération peut prendre quelques minutes. Veuillez patienter.".format(num_days))
                        success = self.data_manager.scrape_fdj_keno_results(num_days=num_days)
                        self._handle_download_result(success)
                elif source_type == 2 or source_type == 3:  # URL personnalisée ou fichier ZIP
                    # Mettre à jour le statut
                    status_var.set(f"Téléchargement depuis {url}...")

                    # Lancer le téléchargement dans un thread séparé
                    def download_thread():
                        try:
                            # Télécharger depuis l'URL spécifiée
                            success = self.data_manager.download_keno_data(url)
                            # Mettre à jour l'interface dans le thread principal
                            self.after(100, lambda: self._handle_download_complete(success, progress_window))
                        except Exception as e:
                            print(f"Erreur lors du téléchargement: {e}")
                            import traceback
                            traceback.print_exc()
                            # Mettre à jour l'interface dans le thread principal
                            self.after(100, lambda: self._handle_download_complete(False, progress_window))

                    # Démarrer le thread
                    threading.Thread(target=download_thread, daemon=True).start()
            except Exception as e:
                progress_window.destroy()
                self.config(cursor="")
                messagebox.showerror("Erreur", "Erreur lors du téléchargement: {}".format(e))

        ttk.Button(buttons_frame, text="Télécharger", command=download, style='Action.TButton', width=15).pack(side=tk.RIGHT, padx=10)

    def _handle_download_result(self, success):
        """Gère le résultat du téléchargement"""
        self.config(cursor="")

        if success:
            draws_count = self.data_manager.get_draws_count()
            messagebox.showinfo("Téléchargement", "Données téléchargées et importées avec succès.\n{} tirages disponibles.".format(draws_count))

            # Mettre à jour les statistiques de base sans déclencher l'auto-amélioration
            self.update_stats_safely()
        else:
            messagebox.showerror("Erreur", "Erreur lors du téléchargement ou de l'importation des données.")

    def _handle_download_complete(self, success, progress_window=None):
        """Gère la fin du téléchargement et ferme la fenêtre de progression"""
        # Fermer la fenêtre de progression si elle existe
        if progress_window:
            try:
                progress_window.destroy()
            except Exception:
                pass

        # Appeler la méthode de gestion du résultat
        self._handle_download_result(success)

    def show_auto_improve_dialog(self):
        """
        🚀 Affiche une fenêtre de dialogue ULTRA-OPTIMISÉE pour l'auto-amélioration
        """
        # Vérifier qu'il y a des données
        if self.data_manager.get_draws_count() < 100:
            messagebox.showerror("Erreur", "Pas assez de données pour l'auto-amélioration (minimum 100 tirages).")
            return

        # Créer une fenêtre de dialogue ultra-stylée
        dialog = tk.Toplevel(self)
        dialog.title("🚀 AUTO-AMÉLIORATION ULTRA-OPTIMISÉE 🚀")
        dialog.geometry("700x800")
        dialog.resizable(True, True)
        dialog.configure(bg='#0a0a0a')

        # Centrer la fenêtre
        dialog.update_idletasks()
        x = self.winfo_rootx() + (self.winfo_width() // 2) - (700 // 2)
        y = self.winfo_rooty() + (self.winfo_height() // 2) - (800 // 2)
        dialog.geometry(f"700x800+{x}+{y}")

        dialog.lift()
        dialog.focus_set()
        dialog.grab_set()

        # Frame principal
        main_frame = tk.Frame(dialog, bg='#0a0a0a')
        main_frame.pack(fill=tk.BOTH, expand=True, padx=15, pady=15)

        # Titre ultra-stylé
        title_label = tk.Label(
            main_frame,
            text="🚀 AUTO-AMÉLIORATION ULTRA-OPTIMISÉE 🚀",
            font=('Helvetica', 20, 'bold'),
            fg='red',
            bg='#0a0a0a'
        )
        title_label.pack(pady=15)

        # Sous-titre
        subtitle_label = tk.Label(
            main_frame,
            text="NOUVELLE GÉNÉRATION D'INTELLIGENCE ARTIFICIELLE",
            font=('Helvetica', 12, 'bold'),
            fg='lime',
            bg='#0a0a0a'
        )
        subtitle_label.pack(pady=5)

        # Description des améliorations
        desc_frame = tk.LabelFrame(main_frame, text="🔥 AMÉLIORATIONS ULTRA-AVANCÉES",
                                  bg='#1a1a1a', fg='white', font=('Helvetica', 12, 'bold'))
        desc_frame.pack(fill=tk.X, pady=15, padx=10)

        improvements_text = """✨ 50+ caractéristiques ultra-avancées par numéro
🧠 Analyse d'entropie et de complexité de Lempel-Ziv
📊 Corrélations inter-numéros sophistiquées
🌊 Analyse de volatilité adaptative
📈 Moments statistiques avancés (skewness, kurtosis)
🔄 Patterns temporels et saisonniers
🎯 Prédictions ultra-précises
⚡ ZÉRO warning affiché
🔥 Performances maximales optimisées"""

        improvements_label = tk.Label(desc_frame, text=improvements_text,
                                    justify=tk.LEFT, bg='#1a1a1a', fg='white',
                                    font=('Helvetica', 10))
        improvements_label.pack(padx=10, pady=10)

        # Variables
        mode_var = tk.StringVar(value="ultra_fast")

        # Options de mode ultra-stylées
        mode_frame = tk.LabelFrame(main_frame, text="🎯 MODES D'ENTRAÎNEMENT ULTRA",
                                  bg='#1a1a1a', fg='white', font=('Helvetica', 12, 'bold'))
        mode_frame.pack(fill=tk.X, pady=15, padx=10)

        # Mode ultra-rapide (recommandé)
        rb1 = tk.Radiobutton(mode_frame, text="🚀 ULTRA-RAPIDE (5 numéros, ~3-5 min) - RECOMMANDÉ",
                           variable=mode_var, value="ultra_fast",
                           fg="lime", bg='#1a1a1a', selectcolor='#333333',
                           font=('Helvetica', 11, 'bold'))
        rb1.pack(anchor=tk.W, padx=15, pady=5)

        # Mode rapide
        rb2 = tk.Radiobutton(mode_frame, text="⚡ RAPIDE (15 numéros, ~10-15 min)",
                           variable=mode_var, value="fast",
                           fg="yellow", bg='#1a1a1a', selectcolor='#333333',
                           font=('Helvetica', 11, 'bold'))
        rb2.pack(anchor=tk.W, padx=15, pady=5)

        # Mode complet
        rb3 = tk.Radiobutton(mode_frame, text="🔥 COMPLET (70 numéros, ~45-60 min)",
                           variable=mode_var, value="complete",
                           fg="red", bg='#1a1a1a', selectcolor='#333333',
                           font=('Helvetica', 11, 'bold'))
        rb3.pack(anchor=tk.W, padx=15, pady=5)

        # Mode complet absolu
        rb4 = tk.Radiobutton(mode_frame, text="💥 COMPLET ABSOLU (70 numéros, TOUTES les données, ~2-4h)",
                           variable=mode_var, value="complete_absolute",
                           fg="purple", bg='#1a1a1a', selectcolor='#333333',
                           font=('Helvetica', 11, 'bold'))
        rb4.pack(anchor=tk.W, padx=15, pady=5)

        # Mode standard (fallback)
        rb5 = tk.Radiobutton(mode_frame, text="📊 STANDARD (ancien système)",
                           variable=mode_var, value="standard",
                           fg="gray", bg='#1a1a1a', selectcolor='#333333',
                           font=('Helvetica', 10))
        rb5.pack(anchor=tk.W, padx=15, pady=5)

        # Performances attendues
        perf_frame = tk.LabelFrame(main_frame, text="🎯 PERFORMANCES ATTENDUES",
                                  bg='#1a1a1a', fg='white', font=('Helvetica', 12, 'bold'))
        perf_frame.pack(fill=tk.X, pady=15, padx=10)

        perf_text = """🎯 F1-Score cible: 0.3+ (exceptionnel pour Keno)
📊 Précision: 75%+
🚀 Amélioration: +100% par rapport au système de base
🎲 Objectif: Passer de 3/7 à 7/7 et de 4/10 à 10/10 !"""

        perf_label = tk.Label(perf_frame, text=perf_text,
                             justify=tk.LEFT, bg='#1a1a1a', fg='lime',
                             font=('Helvetica', 10, 'bold'))
        perf_label.pack(padx=10, pady=10)

        # Fonction de démarrage
        def _start_ultra_improve():
            dialog.destroy()
            mode = mode_var.get()

            if mode == "standard":
                # Utiliser l'ancien système si disponible
                if hasattr(self.analyzer, 'auto_improve'):
                    self.auto_improve_models(fast_mode=True, ultra_fast=False)
                else:
                    messagebox.showinfo("Ancien système", "Ancien système d'auto-amélioration non disponible.")
            else:
                # Utiliser le système ULTRA-OPTIMISÉ
                self._start_ultra_auto_improve(mode)

        # Boutons ultra-stylés
        button_frame = tk.Frame(main_frame, bg='#0a0a0a')
        button_frame.pack(fill=tk.X, pady=25)

        # Bouton DÉMARRER
        start_btn = tk.Button(
            button_frame,
            text="🚀 DÉMARRER L'ULTRA-OPTIMISATION 🚀",
            command=_start_ultra_improve,
            bg="red", fg="white",
            font=('Helvetica', 14, 'bold'),
            width=35, height=2,
            relief=tk.RAISED, borderwidth=3
        )
        start_btn.pack(pady=10)

        # Bouton ANNULER
        cancel_btn = tk.Button(
            button_frame,
            text="Annuler",
            command=dialog.destroy,
            bg="gray", fg="white",
            font=('Helvetica', 12),
            width=20, height=1,
            relief=tk.RAISED, borderwidth=2
        )
        cancel_btn.pack(pady=5)

        # Avertissement
        warning_label = tk.Label(
            main_frame,
            text="⚠️ Le système ULTRA-OPTIMISÉ supprime tous les warnings et optimise les performances",
            font=('Helvetica', 9),
            fg='orange',
            bg='#0a0a0a'
        )
        warning_label.pack(pady=10)

    def _start_ultra_auto_improve(self, mode):
        """Lance l'auto-amélioration ultra-optimisée"""
        try:
            # Utiliser l'optimiseur existant ou en créer un nouveau
            if hasattr(self, 'ultra_optimizer') and self.ultra_optimizer:
                ultra_optimizer = self.ultra_optimizer
                print("🔄 Utilisation de l'optimiseur ultra existant")
            else:
                # Importer le système ultra-optimisé
                from keno_ultra_optimizer import KenoUltraOptimizer
                # Créer l'optimiseur
                ultra_optimizer = KenoUltraOptimizer(self.data_manager, self.analyzer)
                self.ultra_optimizer = ultra_optimizer
                print("🆕 Création d'un nouvel optimiseur ultra")

            # Déterminer les numéros selon le mode
            if mode == "ultra_fast":
                numbers_to_train = [7, 21, 35, 49, 63]
                mode_name = "ULTRA-RAPIDE"
            elif mode == "fast":
                numbers_to_train = [1, 7, 14, 21, 28, 35, 42, 49, 56, 63, 70, 3, 17, 31, 45]
                mode_name = "RAPIDE"
            elif mode == "complete_absolute":
                numbers_to_train = list(range(1, 71))
                mode_name = "COMPLET ABSOLU"
            else:  # complete
                numbers_to_train = list(range(1, 71))
                mode_name = "COMPLET"

            # Créer la fenêtre de progression ultra-stylée
            progress_window = tk.Toplevel(self)
            progress_window.title(f"🚀 AUTO-AMÉLIORATION ULTRA - {mode_name}")
            progress_window.geometry("800x600")
            progress_window.transient(self)
            progress_window.grab_set()
            progress_window.configure(bg='#0a0a0a')

            # Interface ultra-stylée
            title_label = tk.Label(progress_window,
                                  text=f"🚀 AUTO-AMÉLIORATION ULTRA - {mode_name} 🚀",
                                  font=('Helvetica', 16, 'bold'),
                                  fg='red', bg='#0a0a0a')
            title_label.pack(pady=15)

            status_var = tk.StringVar(value="Initialisation du système ultra-avancé...")
            status_label = tk.Label(progress_window, textvariable=status_var,
                                   fg='lime', bg='#0a0a0a',
                                   font=('Helvetica', 12, 'bold'))
            status_label.pack(pady=10)

            # Zone de texte pour les détails
            details_frame = tk.Frame(progress_window, bg='#0a0a0a')
            details_frame.pack(fill=tk.BOTH, expand=True, padx=15, pady=15)

            scrollbar = tk.Scrollbar(details_frame)
            scrollbar.pack(side=tk.RIGHT, fill=tk.Y)

            details_text = tk.Text(details_frame, yscrollcommand=scrollbar.set,
                                  bg='#1a1a1a', fg='white', font=('Consolas', 9))
            details_text.pack(fill=tk.BOTH, expand=True)
            scrollbar.config(command=details_text.yview)

            def update_details(message):
                details_text.insert(tk.END, message + "\n")
                details_text.see(tk.END)
                progress_window.update()

            # Lancer l'entraînement dans un thread
            import threading

            def run_ultra_training():
                try:
                    update_details(f"🚀 Démarrage AUTO-AMÉLIORATION ULTRA - Mode {mode_name}")
                    update_details(f"📊 Entraînement de {len(numbers_to_train)} numéros avec 50+ caractéristiques")
                    status_var.set("Entraînement ultra-optimisé en cours...")

                    trained_count = 0
                    total_f1 = 0

                    # Déterminer les paramètres selon le mode
                    if mode == "complete_absolute":
                        training_mode = "complete_absolute"
                        update_details(f"🔥 MODE COMPLET ABSOLU activé - Utilisation de TOUTES les données disponibles")
                        update_details(f"📊 {self.data_manager.get_draws_count()} tirages seront utilisés")
                    elif mode == "fast" or mode == "ultra_fast":
                        training_mode = "fast"
                    else:
                        training_mode = "complete"

                    for i, number in enumerate(numbers_to_train):
                        update_details(f"\n--- Traitement ULTRA du numéro {number} ({i+1}/{len(numbers_to_train)}) ---")
                        status_var.set(f"Traitement numéro {number} ({i+1}/{len(numbers_to_train)})")

                        # Entraîner le modèle ultra-optimisé avec les bons paramètres
                        if mode == "complete_absolute":
                            # Mode absolu : utiliser toutes les données
                            model_result = ultra_optimizer.train_ultra_model_absolute(number)
                        else:
                            # Modes normaux
                            model_result = ultra_optimizer.train_ultra_model(number, mode=training_mode)

                        if model_result:
                            performance = model_result['performance']
                            f1 = performance['f1_score']
                            accuracy = performance['accuracy']

                            update_details(f"🎯 F1-score: {f1:.4f} | Précision: {accuracy:.4f}")
                            update_details(f"📊 Échantillons de test: {performance['test_size']}")

                            if model_result['ensemble']:
                                update_details(f"🎯 Modèle d'ensemble créé avec {len(model_result['models'])} algorithmes")
                            else:
                                update_details(f"🤖 Modèle {model_result['model_type']} optimisé")

                            total_f1 += f1
                            trained_count += 1

                            update_details(f"✅ Numéro {number} entraîné avec succès")
                        else:
                            update_details(f"❌ Échec de l'entraînement pour le numéro {number}")

                    # Sauvegarder les modèles
                    if trained_count > 0:
                        import os
                        models_path = os.path.join("models", "ultra_optimized_models.pkl")
                        if ultra_optimizer.save_models(models_path):
                            update_details(f"💾 Modèles sauvegardés dans {models_path}")

                    # Résultats finaux
                    if trained_count > 0:
                        avg_f1 = total_f1 / trained_count
                        update_details(f"\n🔥 AUTO-AMÉLIORATION ULTRA TERMINÉE ! 🔥")
                        update_details(f"📊 Numéros entraînés: {trained_count}/{len(numbers_to_train)}")
                        update_details(f"🎯 F1-score moyen: {avg_f1:.4f}")
                        update_details(f"✨ Système prêt pour des prédictions ultra-précises !")

                        # Test de prédiction
                        update_details(f"\n🎲 Test de prédiction ultra-optimisée...")
                        predictions = ultra_optimizer.get_ultra_predictions(10)
                        if predictions:
                            update_details(f"🎯 Top 10 prédictions générées:")
                            for j, pred in enumerate(predictions[:5], 1):
                                update_details(f"  {j}. Numéro {pred['number']} - Probabilité: {pred['probability']:.4f}")

                        status_var.set("🚀 AUTO-AMÉLIORATION ULTRA TERMINÉE AVEC SUCCÈS ! 🚀")
                    else:
                        update_details(f"\n❌ Aucun numéro entraîné avec succès")
                        status_var.set("❌ Échec de l'auto-amélioration ultra")

                except Exception as e:
                    import traceback
                    error_details = traceback.format_exc()
                    update_details(f"\n❌ Erreur: {e}")
                    update_details(f"📋 Détails: {error_details}")
                    status_var.set("❌ Erreur lors de l'auto-amélioration ultra")

                # Bouton de fermeture
                def close_window():
                    progress_window.destroy()

                close_button = tk.Button(progress_window, text="🚀 FERMER",
                                        command=close_window,
                                        bg="red", fg="white",
                                        font=('Helvetica', 12, 'bold'),
                                        padx=25, pady=8)
                close_button.pack(pady=15)

            # Démarrer le thread
            training_thread = threading.Thread(target=run_ultra_training)
            training_thread.daemon = True
            training_thread.start()

        except Exception as e:
            messagebox.showerror("Erreur", f"Erreur auto-amélioration ultra: {e}")

    def _load_ultra_models_if_available(self):
        """Charge automatiquement les modèles ultra-optimisés si disponibles"""
        try:
            models_path = os.path.join("models", "ultra_optimized_models.pkl")

            if os.path.exists(models_path):
                print("🔍 Modèles ultra-optimisés détectés, chargement en cours...")

                # Créer l'optimiseur ultra
                from keno_ultra_optimizer import KenoUltraOptimizer
                self.ultra_optimizer = KenoUltraOptimizer(self.data_manager, self.analyzer)

                # Charger les modèles
                if self.ultra_optimizer.load_models(models_path):
                    print("✅ Modèles ultra-optimisés chargés automatiquement au démarrage")
                    print(f"🎯 {len(self.ultra_optimizer.models)} modèles disponibles")

                    # Afficher un message discret à l'utilisateur
                    self.after(2000, lambda: self._show_models_loaded_message())
                else:
                    print("⚠️ Échec du chargement des modèles ultra-optimisés")
                    self.ultra_optimizer = None
            else:
                print("ℹ️ Aucun modèle ultra-optimisé trouvé (normal si premier démarrage)")

        except Exception as e:
            print(f"❌ Erreur lors du chargement automatique des modèles ultra: {e}")
            self.ultra_optimizer = None

    def _show_models_loaded_message(self):
        """Affiche un message discret que les modèles sont chargés"""
        try:
            if self.ultra_optimizer and self.ultra_optimizer.models:
                count = len(self.ultra_optimizer.models)
                messagebox.showinfo(
                    "Modèles Ultra Chargés",
                    f"✅ {count} modèles ultra-optimisés chargés automatiquement !\n\n"
                    f"🎯 Vos prédictions bénéficient maintenant des modèles entraînés.\n"
                    f"💡 Pas besoin de re-entraîner sauf si vous voulez améliorer."
                )
        except Exception as e:
            print(f"Erreur affichage message: {e}")

    def get_ultra_predictions_if_available(self, count=10):
        """Génère des prédictions ultra si les modèles sont disponibles"""
        try:
            if self.ultra_optimizer and self.ultra_optimizer.models:
                return self.ultra_optimizer.get_ultra_predictions(count)
            else:
                return None
        except Exception as e:
            print(f"Erreur prédictions ultra: {e}")
            return None

    def setup_auto_save(self):
        # Documentation string removed
        try:
            # Vérifier les dépendances avec le module dédié
            try:
                import check_dependencies
                if hasattr(check_dependencies, 'print_dependency_status'):
                    all_ok = check_dependencies.print_dependency_status()
                    if not all_ok:
                        # Demander à l'utilisateur s'il souhaite installer les dépendances manquantes
                        if messagebox.askyesno("Dépendances manquantes",
                                             "Certaines dépendances requises sont manquantes. \n\n"
                                             "Voulez-vous les installer maintenant?"):
                            check_dependencies.install_dependencies()
                else:
                    # Vérification basique des dépendances
                    self._basic_dependency_check()
            except ImportError:
                # Module non disponible, utiliser la vérification basique
                self._basic_dependency_check()
        except Exception as e:
            print("Erreur lors de la vérification des dépendances: {}".format(e))
            # Vérification basique des dépendances
            self._basic_dependency_check()

    def _basic_dependency_check(self):
        # Documentation string removed
        missing = []

        # Vérifier numpy
        if not numpy_available:
            missing.append("numpy")

        # Vérifier matplotlib
        if not matplotlib_available:
            missing.append("matplotlib")

        # Vérifier pandas
        if not pandas_available:
            missing.append("pandas")

        # Vérifier scikit-learn
        if not sklearn_available:
            missing.append("scikit-learn")

        if missing:
            messagebox.showwarning("Dépendances manquantes",
                                "Les bibliothèques suivantes sont manquantes: \n\n"
                                "{0}\n\n"
                                "Certaines fonctionnalités avancées pourraient ne pas être disponibles.".format(', '.join(missing)))

    def _process_add_draw(self, window, date_str, time_str, numbers_str, validate_prediction=False):
        # Documentation string removed
        try:
            # Parser la date
            try:
                date_obj = datetime.strptime(date_str, "%d/%m/%Y").date()
            except ValueError:
                messagebox.showerror("Erreur", "Format de date invalide. Utilisez JJ/MM/AAAA.")
                return

            # Convertir l'heure
            if time_str.lower() == "midi":
                time_obj = datetime.strptime("12:00:00", "%H:%M:%S").time()
            elif time_str.lower() == "soir":
                time_obj = datetime.strptime("19:00:00", "%H:%M:%S").time()
            else:
                messagebox.showerror("Erreur", "Heure invalide. Utilisez 'midi' ou 'soir'.")
                return

            # Combiner date et heure
            draw_date = datetime.combine(date_obj, time_obj)

            # Parser les numéros
            try:
                numbers = [int(n.strip()) for n in numbers_str.split() if n.strip()]

                # Vérifier que les numéros sont valides
                if not numbers:
                    messagebox.showerror("Erreur", "Aucun numéro spécifié.")
                    return

                if len(numbers) != self.numbers_per_draw_var.get():
                    messagebox.showerror("Erreur", "Le nombre de numéros doit être {0}.".format(self.numbers_per_draw_var.get()))
                    return

                for num in numbers:
                    if num < 1 or num > self.max_number_var.get():
                        messagebox.showerror("Erreur", "Les numéros doivent être entre 1 et {0}.".format(self.max_number_var.get()))
                        return
            except ValueError:
                messagebox.showerror("Erreur", "Format de numéros invalide. Utilisez des entiers séparés par des espaces.")
                return

            # Ajouter le tirage
            success = self.data_manager.add_draw(draw_date, numbers)

            if not success:
                messagebox.showerror("Erreur", "Impossible d'ajouter le tirage. Vérifiez les données.")
                return

            # Valider la prédiction si demandé
            if validate_prediction and hasattr(self, 'current_prediction') and self.current_prediction:
                self.validate_prediction(numbers)

            # Fermer la fenêtre
            window.destroy()

            # Afficher un message de succès
            messagebox.showinfo("Succès", "Tirage ajouté avec succès.")

            # Mettre à jour les statistiques
            self.update_stats_safely()

        except Exception as e:
            messagebox.showerror("Erreur", "Erreur lors de l'ajout du tirage: {}".format(e))

    def validate_prediction(self, actual_numbers):
        # Documentation string removed
        if not hasattr(self, 'current_prediction') or not self.current_prediction:
            messagebox.showinfo("Aucune prédiction", "Aucune prédiction à valider.")
            return None

        # Récupérer les numéros prédits
        predicted_numbers = self.current_prediction['numbers']
        method = self.current_prediction['method']

        # Mettre à jour l'apprentissage dans l'analyseur
        try:
            # Vérifier si la méthode update_learning existe
            if hasattr(self.analyzer, 'update_learning'):
                # Utiliser la méthode update_learning pour améliorer les prédictions futures
                stats = self.analyzer.update_learning(predicted_numbers, actual_numbers)
            else:
                # Si la méthode n'existe pas, créer des statistiques basiques
                correct = set(predicted_numbers).intersection(set(actual_numbers))
                stats = {
                    'correct': len(correct),
                    'total': len(predicted_numbers),
                    'accuracy': len(correct) / len(predicted_numbers) * 100 if predicted_numbers else 0
                }

            # Afficher les résultats
            message = "Résultats de la validation:\n\n"
            message += "Méthode: {}\n".format(method)
            message += "Numéros prédits: {}\n".format(sorted(predicted_numbers))
            message += "Numéros réels: {}\n\n".format(sorted(actual_numbers))

            # Vérifier si nous avons des statistiques détaillées ou basiques
            if 'matched_numbers' in stats:
                matches = len(stats['matched_numbers'])
                accuracy = stats['accuracy']
                message += "Correspondances: {} sur {} ({:.2f}%)\n".format(matches, len(predicted_numbers), accuracy)
                message += "Numéros correspondants: {}\n\n".format(sorted(stats['matched_numbers']))
            else:
                matches = stats['correct']
                accuracy = stats['accuracy']
                message += "Correspondances: {} sur {} ({:.2f}%)\n".format(matches, len(predicted_numbers), accuracy)

            message += "Le système a appris de ces résultats pour améliorer les prédictions futures."

            messagebox.showinfo("Validation de prédiction", message)
            return stats
        except Exception as e:
            messagebox.showerror("Erreur", "Erreur lors de la validation de la prédiction: {}".format(e))
            return None

    def run(self):
        """Démarre l'application et charge automatiquement la base de données"""
        try:
            print("=== Démarrage de la méthode run() de KenoGUI ===")

            # Configurer la sauvegarde automatique
            print("Configuration de la sauvegarde automatique...")
            self.setup_auto_save()
            print("Sauvegarde automatique configurée")

            # Configurer le gestionnaire de fermeture
            print("Configuration du gestionnaire de fermeture...")
            self.protocol("WM_DELETE_WINDOW", self._on_closing)
            print("Gestionnaire de fermeture configuré")

            # Charger automatiquement la base de données au démarrage
            print("Tentative de chargement automatique de la base de données...")
            try:
                data_path = "C:\\Users\\<USER>\\Desktop\\Dev\\Projet_2\\data"
                print(f"Chemin de la base de données: {data_path}")
                load_result = self.load_database_from_path(data_path)
                print(f"Résultat du chargement de la base de données: {load_result}")
            except Exception as db_error:
                print(f"Erreur lors du chargement automatique de la base de données: {db_error}")
                import traceback
                traceback.print_exc()
                print("Continuation malgré l'erreur de chargement de la base de données")
                # Continuer même si le chargement de la base de données échoue

            # Démarrer la boucle principale
            print("Démarrage de la boucle principale (mainloop)...")
            self.mainloop()
            print("Boucle principale terminée")
            return True
        except Exception as e:
            print(f"Erreur fatale dans la méthode run(): {e}")
            import traceback
            traceback.print_exc()
            return False

    def load_database_from_path(self, directory_path):
        """Charge automatiquement la base de données depuis le répertoire spécifié"""
        try:
            print(f"=== Début de load_database_from_path({directory_path}) ===")

            # Vérifier si le répertoire existe
            print(f"Vérification de l'existence du répertoire: {directory_path}")
            if not os.path.exists(directory_path):
                print(f"Le répertoire {directory_path} n'existe pas.")
                return False
            print(f"Le répertoire {directory_path} existe.")

            # Chercher les fichiers .keno ou .json dans le répertoire
            print(f"Recherche des fichiers .keno ou .json dans {directory_path}")
            keno_files = [f for f in os.listdir(directory_path) if f.endswith('.keno') or f.endswith('.json')]
            print(f"Fichiers trouvés: {keno_files}")

            if not keno_files:
                print(f"Aucun fichier .keno ou .json trouvé dans {directory_path}")
                return False
            print(f"Nombre de fichiers trouvés: {len(keno_files)}")

            # Trier les fichiers par date de modification (le plus récent d'abord)
            print("Tri des fichiers par date de modification...")
            keno_files.sort(key=lambda f: os.path.getmtime(os.path.join(directory_path, f)), reverse=True)
            print(f"Fichiers triés: {keno_files}")

            # Prendre le fichier le plus récent
            latest_file = keno_files[0]
            file_path = os.path.join(directory_path, latest_file)
            print(f"Fichier le plus récent sélectionné: {latest_file}")
            print(f"Chemin complet du fichier: {file_path}")

            print(f"Chargement automatique de la base de données: {file_path}")

            # Compter les tirages avant le chargement
            draws_before = len(self.data_manager.draws) if hasattr(self.data_manager, 'draws') else 0
            print(f"Nombre de tirages avant chargement: {draws_before}")

            # Vérifier si le fichier existe
            if not os.path.exists(file_path):
                print(f"ERREUR: Le fichier {file_path} n'existe pas!")
                return False
            print(f"Le fichier {file_path} existe et va être chargé.")

            # Vérifier si le fichier est un JSON valide
            if file_path.endswith('.json'):
                print(f"Le fichier {file_path} est un fichier JSON, vérification de sa structure...")
                try:
                    print(f"Ouverture du fichier JSON: {file_path}")
                    with open(file_path, 'r', encoding='utf-8') as f:
                        import json
                        print("Chargement du contenu JSON...")
                        data = json.load(f)
                        print("Contenu JSON chargé avec succès")

                    # Vérifier si le JSON a la structure attendue
                    print("Vérification de la structure du JSON...")
                    if not isinstance(data, dict):
                        print(f"ERREUR: Le fichier JSON {file_path} n'est pas un dictionnaire.")
                        is_valid = False
                    elif 'draws' not in data:
                        print(f"ERREUR: Le fichier JSON {file_path} ne contient pas de clé 'draws'.")
                        is_valid = False
                    else:
                        print(f"Structure JSON valide. Nombre de tirages: {len(data['draws'])}")
                        is_valid = True

                    if not is_valid:
                        print(f"Le fichier JSON {file_path} n'a pas la structure attendue.")
                        # Créer un fichier JSON valide avec une structure vide
                        empty_data = {
                            'max_number': 70,
                            'numbers_per_draw': 20,
                            'draws': []
                        }
                        print(f"Création d'un fichier JSON vide: {file_path}")
                        with open(file_path, 'w', encoding='utf-8') as f:
                            json.dump(empty_data, f, indent=2)
                        print(f"Fichier JSON réinitialisé avec une structure vide.")
                except Exception as json_err:
                    print(f"Erreur lors de la lecture du fichier JSON {file_path}: {json_err}")
                    traceback.print_exc()
                    # Créer un fichier JSON valide avec une structure vide
                    empty_data = {
                        'max_number': 70,
                        'numbers_per_draw': 20,
                        'draws': []
                    }
                    print(f"Création d'un fichier JSON vide après erreur: {file_path}")
                    with open(file_path, 'w', encoding='utf-8') as f:
                        import json
                        json.dump(empty_data, f, indent=2)
                    print(f"Fichier JSON réinitialisé avec une structure vide.")

            # Charger la base de données
            print(f"Tentative de chargement de la base de données: {file_path}")
            try:
                print("Appel de self.data_manager.load_database()...")
                success = self.data_manager.load_database(file_path)
                print(f"Résultat du chargement: {success}")
            except Exception as load_err:
                print(f"Erreur lors du chargement de la base de données: {load_err}")
                traceback.print_exc()
                # Si le chargement échoue, créer un fichier vide
                if file_path.endswith('.json'):
                    print("Création d'un fichier JSON vide après échec de chargement...")
                    empty_data = {
                        'max_number': 70,
                        'numbers_per_draw': 20,
                        'draws': []
                    }
                    with open(file_path, 'w', encoding='utf-8') as f:
                        import json
                        json.dump(empty_data, f, indent=2)
                    print(f"Fichier JSON réinitialisé avec une structure vide.")
                    # Réessayer le chargement
                    try:
                        print("Nouvelle tentative de chargement après réinitialisation...")
                        success = self.data_manager.load_database(file_path)
                        print(f"Résultat du second essai de chargement: {success}")
                    except Exception as retry_err:
                        print(f"Échec du second essai de chargement: {retry_err}")
                        traceback.print_exc()
                        success = False
                else:
                    print("Le fichier n'est pas un JSON, impossible de le réinitialiser.")
                    success = False

            if success:
                draws_count = self.data_manager.get_draws_count()
                draws_after = len(self.data_manager.draws) if hasattr(self.data_manager, 'draws') else 0
                source_files = self.data_manager.get_source_files()

                print(f"Base de données chargée avec succès: {draws_count} tirages disponibles.")
                print(f"Nombre de tirages après chargement: {draws_after}")
                print(f"Différence: {draws_after - draws_before} tirages ajoutés")
                print(f"Fichiers sources inclus: {len(source_files)}")

                # Vérifier les doublons potentiels
                if hasattr(self.data_manager, 'draws') and self.data_manager.draws:
                    # Créer un ensemble pour détecter les doublons
                    draw_ids = set()
                    draw_dates = set()
                    duplicates = 0

                    for draw in self.data_manager.draws:
                        # Vérifier par ID
                        if draw.draw_id and draw.draw_id in draw_ids:
                            duplicates += 1
                            print(f"Doublon détecté par ID: {draw.draw_id} - Date: {draw.draw_date}")
                        elif draw.draw_id:
                            draw_ids.add(draw.draw_id)

                        # Vérifier par date
                        if draw.draw_date and draw.draw_date in draw_dates:
                            print(f"Doublon potentiel par date: {draw.draw_date} - ID: {draw.draw_id}")
                        elif draw.draw_date:
                            draw_dates.add(draw.draw_date)

                    if duplicates > 0:
                        print(f"Attention: {duplicates} doublons détectés")

                # Mettre à jour les statistiques de base sans déclencher l'auto-amélioration
                self.update_stats_safely()
                return True
            else:
                print(f"Erreur lors du chargement de la base de données: {file_path}")
                return False

        except Exception as e:
            print(f"Erreur lors du chargement automatique de la base de données: {e}")
            import traceback
            traceback.print_exc()
            return False

    def _on_closing(self):
        # Documentation string removed
        try:
            # Demander confirmation si des données ont été modifiées
            try:
                if hasattr(self.data_manager, 'is_modified') and self.data_manager.is_modified():
                    if messagebox.askyesno("Confirmation", "Des données ont été modifiées. Voulez-vous les sauvegarder avant de quitter?"):
                        self.save_database()
            except Exception as e:
                print("Erreur lors de la vérification des modifications: {}".format(e))

            # Sauvegarder la configuration
            self.save_config()

            # Nettoyer les ressources
            self._clean_memory()

            # Fermer l'application
            self.destroy()
        except Exception as e:
            print("Erreur lors de la fermeture de l'application: {}".format(e))
            # Forcer la fermeture en cas d'erreur
            self.destroy()

    def auto_improve_models(self, fast_mode=False, ultra_fast=False, max_numbers=None, resume=True, complete_pass=False):
        """
        Lance le processus d'auto-amélioration des modèles de prédiction

        Args:
            fast_mode (bool): Si True, utilise le mode rapide (10 numéros)
            ultra_fast (bool): Si True, utilise le mode ultra-rapide (5 numéros)
            max_numbers (int, optional): Nombre maximum de numéros à entraîner
            resume (bool): Si True, reprend l'analyse en ignorant les numéros déjà traités
            complete_pass (bool): Si True, exécute une passe en mode complet après l'amélioration initiale
        """
        try:
            # Vérifier qu'il y a des données
            if self.data_manager.get_draws_count() < 50:
                messagebox.showerror("Erreur", "Pas assez de données pour l'amélioration automatique (minimum 50 tirages).")
                return

            # Utiliser directement le mode passé en paramètre

            # Afficher une fenêtre de progression plus grande pour montrer plus de détails
            progress_window = tk.Toplevel(self)
            progress_window.title("Amélioration des modèles")
            progress_window.geometry("500x400")  # Fenêtre plus grande
            progress_window.transient(self)
            progress_window.grab_set()
            progress_window.resizable(True, True)  # Permettre le redimensionnement

            # Centrer la fenêtre
            progress_window.update_idletasks()
            width = progress_window.winfo_width()
            height = progress_window.winfo_height()
            x = (self.winfo_width() // 2) - (width // 2) + self.winfo_x()
            y = (self.winfo_height() // 2) - (height // 2) + self.winfo_y()
            progress_window.geometry(f"{width}x{height}+{x}+{y}")

            # Ajouter un titre avec une police plus grande
            title_label = tk.Label(progress_window,
                                 text="Auto-Amélioration des Modèles",
                                 font=("Helvetica", 14, "bold"))
            title_label.pack(pady=10)

            # Ajouter une description
            description = tk.Label(progress_window,
                                text="Ce processus va analyser vos données et optimiser les modèles de prédiction.",
                                wraplength=450)
            description.pack(pady=5)

            # Ajouter un label et une barre de progression
            progress_frame = tk.Frame(progress_window)
            progress_frame.pack(fill=tk.X, padx=20, pady=10)

            progress_label = ttk.Label(progress_frame, text="Progression:")
            progress_label.pack(side=tk.LEFT, padx=5)

            progress_var = tk.DoubleVar()
            progress_bar = ttk.Progressbar(progress_frame, variable=progress_var, maximum=100, length=300)
            progress_bar.pack(side=tk.RIGHT, fill=tk.X, expand=True, padx=5)

            status_var = tk.StringVar(value="Initialisation...")
            status_label = ttk.Label(progress_window, textvariable=status_var, font=("Helvetica", 10))
            status_label.pack(pady=5)

            # Ajouter un bouton Stop pour arrêter le processus
            def stop_process():
                # Définir le drapeau d'arrêt dans l'interface
                self.stop_auto_improve = True

                # Définir également le drapeau dans l'analyseur avancé
                if hasattr(self, 'analyzer') and hasattr(self.analyzer, 'advanced_analyzer'):
                    if hasattr(self.analyzer.advanced_analyzer, 'stop_auto_improve'):
                        self.analyzer.advanced_analyzer.stop_auto_improve = True

                status_var.set("Arrêt en cours... Veuillez patienter.")
                stop_button.config(state="disabled")  # Désactiver le bouton après le clic
                progress_window.update_idletasks()

            stop_button = ttk.Button(progress_window, text="STOP", command=stop_process,
                                   style="Action.TButton", width=15)
            stop_button.pack(pady=10)

            # Lancer l'amélioration en arrière-plan
            threading.Thread(target=self._perform_auto_improve, args=(progress_window, progress_var, status_var, fast_mode, ultra_fast, max_numbers, resume, complete_pass)).start()

        except Exception as e:
            messagebox.showerror("Erreur", f"Erreur lors de l'amélioration automatique: {e}")

    def _stop_auto_improve(self):
        """Arrête le processus d'auto-amélioration en cours"""
        try:
            # Vérifier si l'analyseur avancé est disponible
            if hasattr(self, 'analyzer') and hasattr(self.analyzer, 'advanced_analyzer'):
                # Définir le drapeau d'arrêt dans l'analyseur avancé
                if hasattr(self.analyzer.advanced_analyzer, 'stop_auto_improve'):
                    self.analyzer.advanced_analyzer.stop_auto_improve = True
                    messagebox.showinfo("Arrêt demandé",
                                      "L'arrêt de l'auto-amélioration a été demandé. Le processus s'arrêtera dès que possible.")
                else:
                    messagebox.showwarning("Non disponible",
                                         "L'arrêt de l'auto-amélioration n'est pas disponible dans cette version.")
            else:
                messagebox.showwarning("Non disponible",
                                     "L'analyseur avancé n'est pas disponible.")
        except Exception as e:
            messagebox.showerror("Erreur", f"Erreur lors de l'arrêt de l'auto-amélioration: {e}")

    def _perform_auto_improve(self, progress_window, progress_var, status_var, fast_mode=False, ultra_fast=False, max_numbers=None, resume=True, complete_pass=False):
        """
        Exécute le processus d'auto-amélioration des modèles dans un thread séparé

        Args:
            progress_window (tk.Toplevel): Fenêtre de progression
            progress_var (tk.DoubleVar): Variable pour la barre de progression
            status_var (tk.StringVar): Variable pour le message de statut
            fast_mode (bool): Si True, utilise le mode rapide pour l'entraînement
            ultra_fast (bool): Si True, utilise le mode ultra-rapide pour l'entraînement
            max_numbers (int, optional): Nombre maximum de numéros à entraîner
            resume (bool): Si True, reprend l'analyse en ignorant les numéros déjà traités
            complete_pass (bool): Si True, exécute une passe en mode complet après l'amélioration initiale
        """
        # Créer un drapeau pour indiquer si l'amélioration doit être arrêtée
        self.stop_auto_improve = False

        # Définir également le drapeau dans l'analyseur avancé
        if hasattr(self, 'analyzer') and hasattr(self.analyzer, 'advanced_analyzer'):
            if hasattr(self.analyzer.advanced_analyzer, 'stop_auto_improve'):
                self.analyzer.advanced_analyzer.stop_auto_improve = False

        # Ajouter un gestionnaire d'événement pour la fermeture de la fenêtre
        def on_window_close():
            # Définir le drapeau d'arrêt dans l'interface
            self.stop_auto_improve = True

            # Définir également le drapeau dans l'analyseur avancé
            if hasattr(self, 'analyzer') and hasattr(self.analyzer, 'advanced_analyzer'):
                if hasattr(self.analyzer.advanced_analyzer, 'stop_auto_improve'):
                    self.analyzer.advanced_analyzer.stop_auto_improve = True

            status_var.set("Arrêt en cours... Veuillez patienter.")
            progress_window.update_idletasks()
            # Ne pas détruire la fenêtre immédiatement, laisser le thread le faire
            # Programmer la réinitialisation du drapeau d'arrêt après un délai
            self.after(3000, lambda: setattr(self, 'stop_auto_improve', False))

            # Réinitialiser également le drapeau dans l'analyseur avancé après un délai
            if hasattr(self, 'analyzer') and hasattr(self.analyzer, 'advanced_analyzer'):
                if hasattr(self.analyzer.advanced_analyzer, 'stop_auto_improve'):
                    self.after(3000, lambda: setattr(self.analyzer.advanced_analyzer, 'stop_auto_improve', False))

        # Remplacer le comportement par défaut de fermeture de la fenêtre
        progress_window.protocol("WM_DELETE_WINDOW", on_window_close)
        try:
            # Acquérir le verrou pour éviter les conflits entre threads
            with self.thread_lock:
                # Mettre à jour le statut initial
                status_var.set("Initialisation de l'apprentissage automatique...")
                progress_var.set(5)
                progress_window.update_idletasks()

                # Vérifier les dépendances nécessaires
                missing_deps = []

                if not numpy_available:
                    missing_deps.append("numpy")

                if not pandas_available:
                    missing_deps.append("pandas")

                if not sklearn_available:
                    missing_deps.append("scikit-learn")

                if missing_deps:
                    # Afficher l'erreur dans le thread principal
                    self.after(0, progress_window.destroy)
                    self.after(0, lambda: messagebox.showerror("Dépendances manquantes",
                                                                 "Bibliothèques nécessaires manquantes: {}\n\n"
                                                                 "Veuillez installer les dépendances requises.\n\n"
                                                                 "Commande d'installation:\n"
                                                                 "pip install {}".format(", ".join(missing_deps), " ".join(missing_deps))))
                    return

                # Mettre à jour le statut
                status_var.set("Vérification des données...")
                progress_var.set(10)
                progress_window.update_idletasks()

                # Vérifier qu'il y a suffisamment de données
                if len(self.data_manager.draws) < 50:
                    self.after(0, progress_window.destroy)
                    self.after(0, lambda: messagebox.showwarning("Données insuffisantes",
                                                                   "Il faut au moins 50 tirages pour l'amélioration automatique."))
                    return

                # Agrandir la fenêtre pour afficher plus d'informations
                progress_window.geometry("500x400")
                progress_window.update_idletasks()

                # Ajouter des informations sur les données disponibles
                data_info = tk.Label(progress_window,
                                   text=f"Données disponibles: {len(self.data_manager.draws)} tirages",
                                   font=("Helvetica", 10))
                data_info.pack(pady=5)
                progress_window.update_idletasks()

                # Créer un widget Text pour afficher les détails
                details_frame = tk.Frame(progress_window)
                details_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=5)

                # Ajouter une scrollbar
                scrollbar = tk.Scrollbar(details_frame)
                scrollbar.pack(side=tk.RIGHT, fill=tk.Y)

                # Créer le widget Text
                details_text = tk.Text(details_frame, height=8, width=50, yscrollcommand=scrollbar.set)
                details_text.pack(fill=tk.BOTH, expand=True)
                scrollbar.config(command=details_text.yview)

                # Configurer les tags pour le formatage
                details_text.tag_configure("title", font=("Helvetica", 10, "bold"))
                details_text.tag_configure("info", font=("Helvetica", 9))
                details_text.tag_configure("success", foreground="green")
                details_text.tag_configure("warning", foreground="orange")
                details_text.tag_configure("error", foreground="red")

                # Fonction de mise à jour des détails
                def update_details(message, tag="info"):
                    details_text.insert(tk.END, message + "\n", tag)
                    details_text.see(tk.END)
                    details_text.update_idletasks()

                # Fonction de rappel pour mettre à jour la progression
                def progress_callback(step, total_steps, message):
                    # Vérifier si l'utilisateur a demandé l'arrêt
                    if self.stop_auto_improve:
                        # Signaler l'arrêt demandé par l'utilisateur
                        update_details("Arrêt demandé par l'utilisateur", "warning")
                        # Retourner False pour indiquer que le processus doit s'arrêter
                        return False

                    # Calculer le pourcentage de progression
                    percent = (step / total_steps) * 100

                    # Mettre à jour la barre de progression
                    progress_var.set(percent)

                    # Mettre à jour le message de statut
                    status_var.set(message)

                    # Ajouter le message aux détails
                    update_details(message)

                    # Mettre à jour l'interface
                    progress_window.update_idletasks()

                    # Continuer le processus
                    return True

                # Fonction pour vérifier périodiquement si l'arrêt a été demandé
                def check_stop_requested():
                    if self.stop_auto_improve:
                        # Forcer l'arrêt immédiat du thread d'amélioration
                        status_var.set("Arrêt forcé en cours... Veuillez patienter.")
                        update_details("\nArrêt forcé demandé par l'utilisateur. Interruption des opérations en cours...", "error")
                        progress_window.update_idletasks()

                        # Fonction pour fermer la fenêtre et réinitialiser le drapeau d'arrêt
                        def close_and_reset():
                            self.stop_auto_improve = False  # Réinitialiser le drapeau d'arrêt
                            progress_window.destroy()

                        # Programmer la fermeture de la fenêtre après un court délai
                        progress_window.after(2000, close_and_reset)
                        return False

                    # Programmer une nouvelle vérification dans 500ms
                    progress_window.after(500, check_stop_requested)
                    return True

                # Démarrer la vérification périodique
                progress_window.after(500, check_stop_requested)

                # Mettre à jour le statut
                status_var.set("Démarrage de l'amélioration automatique...")
                progress_var.set(15)
                update_details("Démarrage de l'amélioration automatique...", "title")
                progress_window.update_idletasks()

                # Améliorer les modèles avec la fonction de rappel
                try:
                    # Vérifier si la méthode auto_improve existe
                    if not hasattr(self.analyzer, 'auto_improve'):
                        self.after(0, progress_window.destroy)
                        self.after(0, lambda: messagebox.showerror("Fonctionnalité non disponible",
                                                                    "La fonctionnalité d'amélioration automatique n'est pas disponible.\n"
                                                                    "Vérifiez que vous utilisez la dernière version de l'analyseur."))
                        return

                    # Déterminer le timeout en fonction du mode
                    if ultra_fast:
                        timeout = 1800  # 30 minutes pour le mode ultra-rapide
                    elif fast_mode:
                        timeout = 3600  # 60 minutes pour le mode rapide
                    else:
                        timeout = 21600  # 6 heures pour le mode complet

                    # Lancer l'amélioration avec la fonction de rappel, le mode choisi et le timeout approprié
                    results = self.analyzer.auto_improve(callback=progress_callback,
                                                       fast_mode=fast_mode,
                                                       ultra_fast=ultra_fast,
                                                       max_numbers=max_numbers,
                                                       timeout=timeout,
                                                       resume=resume)

                    # Afficher le mode utilisé
                    if ultra_fast:
                        mode_text = "ULTRA-RAPIDE"
                    elif fast_mode:
                        mode_text = "rapide"
                    else:
                        mode_text = "complet"
                    update_details(f"\nMode d'amélioration utilisé: {mode_text}", "info")

                    # Vérifier si l'utilisateur a demandé l'arrêt
                    if self.stop_auto_improve:
                        # Mettre à jour l'interface pour indiquer l'arrêt
                        progress_var.set(100)
                        status_var.set("Processus arrêté par l'utilisateur")
                        update_details("\nProcessus arrêté par l'utilisateur", "warning")

                        # Ajouter un message explicatif
                        update_details("\nLes modèles peuvent être incomplets ou dans un état intermédiaire.", "warning")
                        update_details("Il est recommandé de relancer l'auto-amélioration complète ultérieurement.", "info")
                    else:
                        # Mettre à jour la progression finale pour un processus terminé normalement
                        progress_var.set(100)
                        status_var.set("Amélioration terminée!")
                        update_details("\nAmélioration terminée!", "title")

                    # Afficher les résultats détaillés
                    if results['success']:
                        update_details("\nRésultat: Succès", "success")
                    else:
                        update_details("\nRésultat: Échec", "warning")

                    update_details(f"\nMessage: {results['message']}")

                    # Afficher les statistiques
                    if 'stats' in results and results['stats']:
                        update_details("\nStatistiques:", "title")

                        # Afficher d'abord les informations sur le mode utilisé
                        stats = results['stats']
                        mode_info = ""
                        if stats.get('ultra_fast', False):
                            mode_info = "ULTRA-RAPIDE"
                        elif stats.get('fast_mode', False):
                            mode_info = "rapide"
                        else:
                            mode_info = "complet"

                        update_details(f"- Mode utilisé: {mode_info}", "info")

                        if 'max_numbers' in stats:
                            update_details(f"- Nombre de numéros limité à: {stats['max_numbers']}", "info")

                        # Afficher les autres statistiques importantes en premier
                        if 'models' in stats:
                            update_details(f"- Modèles entraînés: {stats['models']}", "info")
                        if 'success_rate' in stats:
                            update_details(f"- Taux de réussite: {stats['success_rate']:.2f}%", "info")

                        # Afficher les autres statistiques
                        for key, value in stats.items():
                            if key not in ['models', 'success_rate', 'fast_mode', 'ultra_fast', 'max_numbers']:
                                update_details(f"- {key}: {value}")

                    # Fonction pour fermer la fenêtre et réinitialiser le drapeau d'arrêt
                    def close_window():
                        self.stop_auto_improve = False  # Réinitialiser le drapeau d'arrêt
                        progress_window.destroy()

                    # Ajouter un bouton pour fermer la fenêtre
                    close_button = ttk.Button(progress_window, text="Fermer", command=close_window)
                    close_button.pack(pady=10)

                    # Remplacer le comportement par défaut de fermeture de la fenêtre
                    progress_window.protocol("WM_DELETE_WINDOW", close_window)

                    # Exécuter une passe en mode complet si demandé et si le processus n'a pas été arrêté
                    if complete_pass and not self.stop_auto_improve and results['success']:
                        update_details("\nExécution d'une passe supplémentaire en mode complet...", "title")
                        status_var.set("Exécution d'une passe en mode complet...")
                        progress_var.set(0)  # Réinitialiser la barre de progression
                        progress_window.update_idletasks()

                        # Déterminer le timeout pour le mode complet
                        complete_timeout = 21600  # 6 heures pour le mode complet

                        # Lancer l'amélioration en mode complet
                        update_details("Démarrage de la passe en mode complet...", "info")
                        complete_results = self.analyzer.auto_improve(callback=progress_callback,
                                                                   fast_mode=False,
                                                                   ultra_fast=False,
                                                                   max_numbers=None,  # Tous les numéros
                                                                   timeout=complete_timeout,
                                                                   resume=True)  # Toujours reprendre pour la passe complète

                        # Afficher les résultats de la passe complète
                        if complete_results['success']:
                            update_details("\nPasse en mode complet terminée avec succès!", "success")
                            # Mettre à jour les résultats globaux avec ceux de la passe complète
                            results = complete_results
                        else:
                            update_details("\nPasse en mode complet interrompue ou échouée.", "warning")
                            update_details(f"Message: {complete_results['message']}")

                    # Sauvegarder les données d'apprentissage si possible et si le processus n'a pas été arrêté
                    if not self.stop_auto_improve and results['success'] and learning_data_module_available:
                        try:
                            update_details("\nSauvegarde des données d'apprentissage...", "info")
                            learning_data_path = os.path.join(os.path.dirname(os.path.abspath(__file__)), "learning_data.json")
                            save_success = save_learning_data(self.analyzer, learning_data_path)
                            if save_success:
                                update_details("Données d'apprentissage sauvegardées avec succès.", "success")
                            else:
                                update_details("Impossible de sauvegarder les données d'apprentissage.", "warning")
                        except Exception as e:
                            update_details(f"Erreur lors de la sauvegarde des données d'apprentissage: {e}", "error")
                    elif self.stop_auto_improve:
                        update_details("\nLes données d'apprentissage n'ont pas été sauvegardées car le processus a été arrêté.", "warning")

                    # Ne pas fermer automatiquement la fenêtre pour permettre à l'utilisateur de voir les résultats

                except AttributeError as e:
                    # Si la méthode auto_improve n'existe pas ou a une signature différente
                    update_details(f"Erreur d'attribut: {e}", "error")
                    self.after(0, progress_window.destroy)
                    self.after(0, lambda: messagebox.showerror("Fonctionnalité non disponible",
                                                                "La fonctionnalité d'amélioration automatique n'est pas disponible.\n"
                                                                f"Erreur: {e}"))

        except Exception as e:
            # Journaliser l'erreur pour le débogage
            print(f"Erreur lors de l'amélioration automatique: {e}")

            # Fermer la fenêtre de progression dans le thread principal
            self.after(0, progress_window.destroy)

            # Afficher l'erreur dans le thread principal
            self.after(0, lambda: messagebox.showerror("Erreur",
                                                        f"Erreur lors de l'amélioration automatique: {e}\n\n"
                                                        "Vérifiez que toutes les dépendances sont installées."))
