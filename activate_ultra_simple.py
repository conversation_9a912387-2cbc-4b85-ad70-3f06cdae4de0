#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Script pour activer le système ULTRA-OPTIMISÉ SIMPLIFIÉ
Version stable qui évite les conflits de parallélisation
"""

import os

def integrate_ultra_simple():
    """Intègre le système ultra-optimisé simplifié dans l'interface"""
    
    print("🚀 ACTIVATION DU SYSTÈME ULTRA-OPTIMISÉ SIMPLIFIÉ")
    print("💻 Version stable pour éviter les conflits")
    print("⚡ Optimisation efficace avec 8 cœurs\n")
    
    gui_file = 'keno_gui.py'
    
    if not os.path.exists(gui_file):
        print(f"❌ Fichier {gui_file} non trouvé")
        return False
    
    try:
        # Lire le fichier
        with open(gui_file, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # Vérifier si déjà modifié
        if 'KenoUltraSimple' in content:
            print("✅ Système ultra-optimisé simplifié déjà intégré")
            return True
        
        # Ajouter l'import du système ultra-optimisé simplifié
        ultra_import = """
# === SYSTÈME ULTRA-OPTIMISÉ SIMPLIFIÉ ===
try:
    from keno_ultra_simple import KenoUltraSimple
    ultra_simple_available = True
    print("🚀 Système ULTRA-OPTIMISÉ SIMPLIFIÉ disponible")
except ImportError as e:
    ultra_simple_available = False
    print(f"❌ Système ULTRA-OPTIMISÉ SIMPLIFIÉ non disponible: {e}")
"""
        
        # Trouver où insérer l'import
        import_pos = content.find("from keno_data import KenoDataManager")
        if import_pos != -1:
            content = content[:import_pos] + ultra_import + "\n" + content[import_pos:]
        
        # Remplacer la méthode _start_ultra_auto_improve
        method_start = content.find("def _start_ultra_auto_improve(self, mode):")
        if method_start != -1:
            # Trouver la fin de la méthode
            method_end = content.find("\n    def ", method_start + 1)
            if method_end == -1:
                method_end = content.find("\n    def setup_auto_save", method_start)
            
            if method_end != -1:
                # Nouvelle méthode ultra-optimisée simplifiée
                new_method = '''    def _start_ultra_auto_improve(self, mode):
        """Lance l'auto-amélioration ULTRA-OPTIMISÉE SIMPLIFIÉE (stable)"""
        try:
            # Vérifier la disponibilité du système ultra-optimisé simplifié
            if not ultra_simple_available:
                messagebox.showerror("Erreur", "Système ULTRA-OPTIMISÉ SIMPLIFIÉ non disponible")
                return
            
            # Créer le système ultra-optimisé simplifié
            ultra_simple = KenoUltraSimple(self.data_manager, self.analyzer)
            
            # Déterminer les numéros selon le mode
            if mode == "ultra_fast":
                numbers_count = 5
                mode_name = "ULTRA-RAPIDE"
                estimated_time = "5-8 minutes"
            elif mode == "fast":
                numbers_count = 15
                mode_name = "RAPIDE"
                estimated_time = "15-25 minutes"
            else:  # complete
                numbers_count = 70
                mode_name = "COMPLET"
                estimated_time = "60-90 minutes"
            
            # Créer la fenêtre de progression ultra-optimisée
            progress_window = tk.Toplevel(self)
            progress_window.title(f"🚀 ULTRA-OPTIMISÉ SIMPLIFIÉ - {mode_name}")
            progress_window.geometry("900x700")
            progress_window.transient(self)
            progress_window.grab_set()
            progress_window.configure(bg='#0a0a0a')
            
            # Interface ultra-stylée
            title_label = tk.Label(progress_window, 
                                  text=f"🚀 ULTRA-OPTIMISÉ SIMPLIFIÉ - {mode_name} 🚀", 
                                  font=('Helvetica', 18, 'bold'),
                                  fg='red', bg='#0a0a0a')
            title_label.pack(pady=15)
            
            # Informations sur la version
            version_label = tk.Label(progress_window, 
                                    text="💻 Version stable | ⚡ 8 cœurs optimisés | 🔥 Évite les conflits", 
                                    font=('Helvetica', 12, 'bold'),
                                    fg='lime', bg='#0a0a0a')
            version_label.pack(pady=5)
            
            status_var = tk.StringVar(value=f"Initialisation ultra-optimisée ({numbers_count} numéros, ~{estimated_time})...")
            status_label = tk.Label(progress_window, textvariable=status_var,
                                   fg='yellow', bg='#0a0a0a',
                                   font=('Helvetica', 12, 'bold'))
            status_label.pack(pady=10)
            
            # Zone de texte pour les détails
            details_frame = tk.Frame(progress_window, bg='#0a0a0a')
            details_frame.pack(fill=tk.BOTH, expand=True, padx=15, pady=15)
            
            scrollbar = tk.Scrollbar(details_frame)
            scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
            
            details_text = tk.Text(details_frame, yscrollcommand=scrollbar.set,
                                  bg='#1a1a1a', fg='white', font=('Consolas', 9))
            details_text.pack(fill=tk.BOTH, expand=True)
            scrollbar.config(command=details_text.yview)
            
            def update_details(message):
                details_text.insert(tk.END, message + "\\n")
                details_text.see(tk.END)
                progress_window.update()
            
            # Lancer l'entraînement ultra-optimisé dans un thread
            import threading
            
            def run_ultra_simple_training():
                try:
                    update_details(f"🚀 DÉMARRAGE ULTRA-OPTIMISÉ SIMPLIFIÉ - Mode {mode_name}")
                    update_details(f"💻 Version stable pour éviter les conflits")
                    update_details(f"⚡ Optimisation avec 8 cœurs")
                    update_details(f"📊 Entraînement: {numbers_count} numéros avec 50+ caractéristiques")
                    
                    status_var.set("🔥 PHASE 1: Création des caractéristiques ultra-avancées...")
                    
                    # Lancer l'entraînement ultra-optimisé simplifié
                    result = ultra_simple.run_ultra_simple_training(mode)
                    
                    if result['success']:
                        update_details(f"\\n🎉 ULTRA-OPTIMISÉ SIMPLIFIÉ TERMINÉ AVEC SUCCÈS !")
                        update_details(f"✅ Modèles entraînés: {result['trained_models']}/{result['total_numbers']}")
                        update_details(f"🎯 F1-score moyen: {result['avg_f1']:.4f}")
                        update_details(f"📊 Précision moyenne: {result['avg_accuracy']:.4f}")
                        update_details(f"💻 Optimisation: 8 cœurs utilisés efficacement")
                        update_details(f"🚀 Système prêt pour des prédictions ultra-précises !")
                        
                        status_var.set("🎉 ULTRA-OPTIMISÉ SIMPLIFIÉ TERMINÉ AVEC SUCCÈS ! 🎉")
                    else:
                        update_details(f"\\n❌ Échec de l'entraînement ultra-optimisé")
                        status_var.set("❌ Échec de l'entraînement")
                        
                except Exception as e:
                    update_details(f"\\n💥 Erreur ultra-optimisée: {e}")
                    status_var.set("💥 Erreur système")
                    import traceback
                    update_details(traceback.format_exc())
                
                # Bouton de fermeture
                def close_window():
                    progress_window.destroy()
                
                close_button = tk.Button(progress_window, text="🚀 FERMER", 
                                        command=close_window,
                                        bg="red", fg="white", 
                                        font=('Helvetica', 14, 'bold'),
                                        padx=30, pady=10)
                close_button.pack(pady=20)
            
            # Démarrer le thread ultra-optimisé
            training_thread = threading.Thread(target=run_ultra_simple_training)
            training_thread.daemon = True
            training_thread.start()
            
        except Exception as e:
            messagebox.showerror("Erreur", f"Erreur ultra-optimisée: {e}")

'''
                
                # Remplacer l'ancienne méthode
                content = content[:method_start] + new_method + content[method_end:]
        
        # Écrire le fichier modifié
        with open(gui_file, 'w', encoding='utf-8') as f:
            f.write(content)
        
        print("✅ Système ultra-optimisé simplifié intégré avec succès")
        return True
        
    except Exception as e:
        print(f"❌ Erreur lors de l'intégration: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_simple_system():
    """Test du système ultra-optimisé simplifié"""
    
    print("\\n🧪 TEST DU SYSTÈME ULTRA-OPTIMISÉ SIMPLIFIÉ")
    
    try:
        from keno_ultra_simple import test_ultra_simple
        success = test_ultra_simple()
        
        if success:
            print("✅ Test réussi - Système stable opérationnel")
            return True
        else:
            print("❌ Test échoué")
            return False
            
    except Exception as e:
        print(f"❌ Erreur lors du test: {e}")
        return False

def main():
    """Fonction principale"""
    
    print("🚀 ACTIVATION DU SYSTÈME ULTRA-OPTIMISÉ SIMPLIFIÉ")
    print("💻 Version stable qui évite les conflits")
    print("⚡ Optimisation efficace avec 8 cœurs")
    print("🔥 Performances ultra-avancées\\n")
    
    # 1. Tester le système
    test_success = test_simple_system()
    
    if not test_success:
        print("\\n❌ Le test a échoué. Vérifiez les dépendances.")
        return 1
    
    # 2. Intégrer dans l'interface
    integration_success = integrate_ultra_simple()
    
    if integration_success:
        print("\\n🎉 ACTIVATION RÉUSSIE !")
        print("✅ Système ultra-optimisé simplifié intégré")
        print("💻 Version stable qui évite les conflits")
        print("⚡ Utilisation efficace de 8 cœurs")
        print("\\n🚀 Relancez votre application:")
        print("  python main.py")
        print("\\n🎯 Cliquez sur 'LANCER L'AUTO-AMÉLIORATION' pour voir la différence !")
        print("💡 Cette version est stable et évite les erreurs de parallélisation !")
        return 0
    else:
        print("\\n❌ ÉCHEC DE L'ACTIVATION")
        return 1

if __name__ == "__main__":
    try:
        exit_code = main()
        exit(exit_code)
    except Exception as e:
        print(f"Erreur: {e}")
        import traceback
        traceback.print_exc()
        exit(1)
