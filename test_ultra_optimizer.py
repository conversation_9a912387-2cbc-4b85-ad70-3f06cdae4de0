#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Script de test pour l'optimiseur ultra-avancé Keno
"""

import os
import sys
import warnings

# Suppression des warnings
warnings.filterwarnings('ignore')
os.environ['TF_CPP_MIN_LOG_LEVEL'] = '3'

def test_ultra_optimizer():
    """Test complet de l'optimiseur ultra"""
    print("🚀 TEST DE L'OPTIMISEUR ULTRA-AVANCÉ KENO 🚀")
    print("=" * 60)
    
    try:
        # 1. Import des modules
        print("📦 Import des modules...")
        from keno_data import KenoDataManager
        from keno_analyzer import KenoAnalyzer
        from keno_ultra_optimizer import KenoUltraOptimizer
        print("✅ Modules importés avec succès")
        
        # 2. Initialisation des composants
        print("\n🔧 Initialisation des composants...")
        data_manager = KenoDataManager()
        analyzer = KenoAnalyzer(data_manager)
        ultra_optimizer = KenoUltraOptimizer(data_manager, analyzer)
        print("✅ Composants initialisés")
        
        # 3. Chargement des données
        print("\n📊 Chargement des données...")
        data_path = os.path.join("data", "datafull.keno")
        if os.path.exists(data_path):
            success = data_manager.load_database(data_path)
            if success:
                draws_count = data_manager.get_draws_count()
                print(f"✅ {draws_count} tirages chargés")
            else:
                print("❌ Échec du chargement des données")
                return False
        else:
            print(f"❌ Fichier de données non trouvé: {data_path}")
            return False
        
        # 4. Test de création de caractéristiques
        print("\n🧠 Test de création de caractéristiques...")
        test_number = 7
        result = ultra_optimizer.create_ultra_features(test_number)
        if result:
            X, y, feature_names = result
            print(f"✅ {len(feature_names)} caractéristiques créées pour {len(X)} échantillons")
            print(f"📊 Exemples de caractéristiques: {feature_names[:5]}")
        else:
            print("❌ Échec de création des caractéristiques")
            return False
        
        # 5. Test d'entraînement de modèle
        print(f"\n🤖 Test d'entraînement pour le numéro {test_number}...")
        model_result = ultra_optimizer.train_ultra_model(test_number, mode="fast")
        if model_result:
            perf = model_result['performance']
            print(f"✅ Modèle entraîné avec succès")
            print(f"🎯 F1-score: {perf['f1_score']:.4f}")
            print(f"📊 Précision: {perf['accuracy']:.4f}")
            print(f"🔢 Échantillons de test: {perf['test_size']}")
            
            if model_result['ensemble']:
                print(f"🎯 Modèle d'ensemble avec {len(model_result['models'])} algorithmes")
            else:
                print(f"🤖 Modèle {model_result['model_type']}")
        else:
            print("❌ Échec de l'entraînement du modèle")
            return False
        
        # 6. Test de prédiction
        print(f"\n🔮 Test de prédiction pour le numéro {test_number}...")
        probability = ultra_optimizer.predict_number_probability(test_number)
        print(f"✅ Probabilité calculée: {probability:.4f}")
        
        # 7. Test de prédictions multiples
        print("\n🎲 Test de prédictions multiples...")
        predictions = ultra_optimizer.get_ultra_predictions(5)
        if predictions:
            print(f"✅ {len(predictions)} prédictions générées:")
            for i, pred in enumerate(predictions, 1):
                print(f"  {i}. Numéro {pred['number']} - Probabilité: {pred['probability']:.4f}")
        else:
            print("❌ Aucune prédiction générée")
        
        # 8. Test de sauvegarde/chargement
        print("\n💾 Test de sauvegarde/chargement...")
        models_path = os.path.join("models", "test_ultra_models.pkl")
        
        # Sauvegarde
        if ultra_optimizer.save_models(models_path):
            print("✅ Modèles sauvegardés")
        else:
            print("❌ Échec de sauvegarde")
            return False
        
        # Nouveau optimiseur pour test de chargement
        ultra_optimizer_2 = KenoUltraOptimizer(data_manager, analyzer)
        if ultra_optimizer_2.load_models(models_path):
            print("✅ Modèles chargés")
            
            # Test de prédiction avec modèles chargés
            prob_loaded = ultra_optimizer_2.predict_number_probability(test_number)
            print(f"🔮 Probabilité avec modèle chargé: {prob_loaded:.4f}")
        else:
            print("❌ Échec de chargement")
            return False
        
        print("\n🎉 TOUS LES TESTS RÉUSSIS ! 🎉")
        print("✨ L'optimiseur ultra-avancé fonctionne parfaitement")
        return True
        
    except Exception as e:
        print(f"\n❌ ERREUR LORS DU TEST: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_ultra_training_modes():
    """Test des différents modes d'entraînement"""
    print("\n🎯 TEST DES MODES D'ENTRAÎNEMENT")
    print("=" * 40)
    
    try:
        from keno_data import KenoDataManager
        from keno_analyzer import KenoAnalyzer
        from keno_ultra_optimizer import KenoUltraOptimizer
        
        data_manager = KenoDataManager()
        analyzer = KenoAnalyzer(data_manager)
        ultra_optimizer = KenoUltraOptimizer(data_manager, analyzer)
        
        # Charger les données
        data_path = os.path.join("data", "datafull.keno")
        if not data_manager.load_database(data_path):
            print("❌ Impossible de charger les données")
            return False
        
        test_numbers = [7, 21, 35]  # Quelques numéros de test
        modes = ["fast", "complete"]
        
        for mode in modes:
            print(f"\n🚀 Test mode {mode.upper()}...")
            mode_results = []
            
            for number in test_numbers:
                print(f"  Entraînement numéro {number}...")
                result = ultra_optimizer.train_ultra_model(number, mode=mode)
                if result:
                    perf = result['performance']
                    mode_results.append(perf['f1_score'])
                    print(f"    ✅ F1: {perf['f1_score']:.4f}, Acc: {perf['accuracy']:.4f}")
                else:
                    print(f"    ❌ Échec")
            
            if mode_results:
                avg_f1 = sum(mode_results) / len(mode_results)
                print(f"  📊 F1-score moyen en mode {mode}: {avg_f1:.4f}")
        
        return True
        
    except Exception as e:
        print(f"❌ Erreur: {e}")
        return False

if __name__ == "__main__":
    print("🧪 DÉMARRAGE DES TESTS ULTRA-OPTIMISÉS")
    print("=" * 60)
    
    # Test principal
    success1 = test_ultra_optimizer()
    
    # Test des modes
    success2 = test_ultra_training_modes()
    
    print("\n" + "=" * 60)
    if success1 and success2:
        print("🎉 TOUS LES TESTS RÉUSSIS ! L'OPTIMISEUR ULTRA EST OPÉRATIONNEL ! 🎉")
        print("✨ Vous pouvez maintenant utiliser l'auto-amélioration ultra dans l'interface")
    else:
        print("❌ CERTAINS TESTS ONT ÉCHOUÉ")
        print("🔧 Vérifiez les erreurs ci-dessus pour diagnostiquer le problème")
    
    print("=" * 60)
